// src/world_info_optimizer/store.ts

import { INITIAL_ACTIVE_TAB } from './constants';
import { AppState, LorebookEntry, LorebookFile, TavernRegex } from './types';

// --- State Definition ---

const initialState: AppState = {
  regexes: { global: [], character: [] },
  lorebooks: { character: [] },
  chatLorebook: null,
  allLorebooks: [],
  lorebookEntries: new Map<string, LorebookEntry[]>(),
  lorebookUsage: new Map<string, string[]>(),
  activeTab: INITIAL_ACTIVE_TAB,
  isDataLoaded: false,
  isLoading: false,
  loadError: null,
  searchFilters: {
    bookName: true,
    entryName: true,
    keywords: true,
    content: true,
  },
  searchQuery: '',
  multiSelectMode: false,
  selectedItems: new Set<string>(),
  collapsedBooks: new Set<string>(),
};

// 使用深拷贝来创建可变状态，避免直接修改initialState
let state: AppState = JSON.parse(JSON.stringify(initialState));
// Map和Set不能通过JSON.stringify/parse正确克隆，需要手动重新创建
state.lorebookEntries = new Map<string, LorebookEntry[]>();
state.lorebookUsage = new Map<string, string[]>();
state.selectedItems = new Set<string>();
state.collapsedBooks = new Set<string>();
state.isLoading = false;
state.loadError = null;

// --- Listeners for State Changes ---

type Listener = (newState: AppState) => void;
const listeners: Listener[] = [];

/**
 * 订阅状态变化。
 * @param listener 当状态更新时要调用的回调函数。
 * @returns 一个取消订阅的函数。
 */
export const subscribe = (listener: Listener) => {
  listeners.push(listener);
  return () => {
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  };
};

/**
 * 通知所有监听器状态已更新。
 */
const notify = () => {
  // 传递状态的深拷贝，以防监听器意外修改状态
  const deepCopiedState = {
    ...state,
    // 深拷贝 Map，包括其内部的数组
    lorebookEntries: new Map(
      Array.from(state.lorebookEntries.entries()).map(([key, value]) => [
        key,
        [...value], // 复制数组
      ]),
    ),
    // 深拷贝 Map，包括其内部的数组
    lorebookUsage: new Map(
      Array.from(state.lorebookUsage.entries()).map(([key, value]) => [
        key,
        [...value], // 复制数组
      ]),
    ),
    selectedItems: new Set(state.selectedItems),
    collapsedBooks: new Set(state.collapsedBooks),
    // 深拷贝嵌套对象
    regexes: {
      global: [...state.regexes.global],
      character: [...state.regexes.character],
    },
    lorebooks: {
      character: [...state.lorebooks.character],
    },
    allLorebooks: [...state.allLorebooks],
    searchFilters: { ...state.searchFilters },
  };
  listeners.forEach(listener => listener(deepCopiedState));
};

// --- State Accessors and Mutators ---

/**
 * 获取当前状态对象的快照。
 * @returns 当前应用状态的深拷贝。
 */
export const getState = (): AppState => {
  // 返回深拷贝以保证状态的不可变性
  return {
    ...state,
    // 深拷贝 Map，包括其内部的数组
    lorebookEntries: new Map(
      Array.from(state.lorebookEntries.entries()).map(([key, value]) => [
        key,
        [...value], // 复制数组
      ]),
    ),
    // 深拷贝 Map，包括其内部的数组
    lorebookUsage: new Map(
      Array.from(state.lorebookUsage.entries()).map(([key, value]) => [
        key,
        [...value], // 复制数组
      ]),
    ),
    selectedItems: new Set(state.selectedItems),
    collapsedBooks: new Set(state.collapsedBooks),
    // 深拷贝嵌套对象
    regexes: {
      global: [...state.regexes.global],
      character: [...state.regexes.character],
    },
    lorebooks: {
      character: [...state.lorebooks.character],
    },
    allLorebooks: [...state.allLorebooks],
    searchFilters: { ...state.searchFilters },
  };
};

/**
 * 更新应用状态并通知所有订阅者。
 * @param updater 一个函数，接收当前状态并返回一个新的（或修改过的）状态部分。
 */
export const updateState = (updater: (currentState: AppState) => Partial<AppState>) => {
  const updates = updater(state);
  state = { ...state, ...updates };
  notify();
};

/**
 * 重置整个应用状态到初始值。
 */
export const resetState = () => {
  state = JSON.parse(JSON.stringify(initialState));
  state.lorebookEntries = new Map<string, LorebookEntry[]>();
  state.lorebookUsage = new Map<string, string[]>();
  state.selectedItems = new Set<string>();
  state.collapsedBooks = new Set<string>();
  notify();
};

// --- Specific State Updater Functions ---

export const setActiveTab = (tabId: string) => {
  updateState(s => ({ ...s, activeTab: tabId }));
};

export const setDataLoaded = (isLoaded: boolean) => {
  updateState(s => ({ ...s, isDataLoaded: isLoaded }));
};

export const setLoading = (isLoading: boolean) => {
  updateState(s => ({ ...s, isLoading }));
};

export const setLoadError = (error: string | null) => {
  updateState(s => ({ ...s, loadError: error }));
};

export const setSearchQuery = (query: string) => {
  updateState(s => ({ ...s, searchQuery: query }));
};

export const setAllData = (data: {
  globalRegexes: TavernRegex[];
  characterRegexes: TavernRegex[];
  allLorebooks: LorebookFile[];
  characterLorebooks: string[];
  chatLorebook: string | null;
  lorebookEntries: Map<string, LorebookEntry[]>;
  lorebookUsage: Map<string, string[]>;
}) => {
  updateState(s => ({
    ...s,
    regexes: { global: data.globalRegexes, character: data.characterRegexes },
    allLorebooks: data.allLorebooks,
    lorebooks: { character: data.characterLorebooks },
    chatLorebook: data.chatLorebook,
    lorebookEntries: data.lorebookEntries,
    lorebookUsage: data.lorebookUsage,
    isDataLoaded: true,
  }));
};

// --- Multi-Select and Collapse State Management ---

export const toggleMultiSelectMode = () => {
  updateState(s => ({
    ...s,
    multiSelectMode: !s.multiSelectMode,
    selectedItems: new Set(), // 清空选中项
  }));
};

export const toggleItemSelection = (itemId: string) => {
  updateState(s => {
    const newSelectedItems = new Set(s.selectedItems);
    if (newSelectedItems.has(itemId)) {
      newSelectedItems.delete(itemId);
    } else {
      newSelectedItems.add(itemId);
    }
    return { ...s, selectedItems: newSelectedItems };
  });
};

export const toggleBookCollapse = (bookName: string) => {
  updateState(s => {
    const newCollapsedBooks = new Set(s.collapsedBooks);
    if (newCollapsedBooks.has(bookName)) {
      newCollapsedBooks.delete(bookName);
    } else {
      newCollapsedBooks.add(bookName);
    }
    return { ...s, collapsedBooks: newCollapsedBooks };
  });
};

export const collapseAllBooks = () => {
  const state = getState();
  const allBookNames = new Set<string>();

  // 收集所有世界书名称
  state.allLorebooks.forEach(book => allBookNames.add(book.name));
  state.lorebooks.character.forEach(bookName => allBookNames.add(bookName));
  if (state.chatLorebook) allBookNames.add(state.chatLorebook);

  updateState(s => ({ ...s, collapsedBooks: allBookNames }));
};

export const expandAllBooks = () => {
  updateState(s => ({ ...s, collapsedBooks: new Set() }));
};
