<![CDATA[
# UI Style Guide (WIO v3 Refactor)

This document outlines the design principles and core styling conventions for the World Info Optimizer (WIO) plugin's user interface.

## 1. Core Design Principles

The UI follows three core principles:

- **Flat**: We avoid heavy shadows, gradients, and complex 3D effects. The interface is clean, sharp, and content-focused. Borders are used sparingly, primarily for separation.
- **Dark**: The entire UI is built on a dark theme to reduce eye strain and integrate seamlessly with modern dark-mode applications. The color palette is consistent and uses high-contrast text for readability.
- **Compact**: Spacing, font sizes, and component dimensions are optimized for information density, allowing users to see more content at once without feeling cluttered. The layout is functional and efficient.

## 2. Core Styling Files

All global styles and CSS variables for the main plugin panel are defined within:
- [`src/world_info_optimizer/ui.ts`](../src/world_info_optimizer/ui.ts)

Example Vue components and their scoped styles can be found in:
- `src/界面示例/`

## 3. Core CSS Variables

The entire UI is themed using CSS variables defined in `:root`. Below are the key variables to use for any new component development.

### Color Palette

- ` --wio-bg-primary: #1f1f1f;` (Main background)
- ` --wio-bg-secondary: #2d2d2d;` (Secondary background, for cards and lists)
- ` --wio-bg-tertiary: #3c3c3c;` (Tertiary background, for headers and hover states)
- ` --wio-bg-toolbar: #252525;` (Toolbar background)
- ` --wio-text-primary: #e0e0e0;` (Primary text color)
- ` --wio-text-secondary: #9e9e9e;` (Secondary text color, for placeholders and hints)
- ` --wio-highlight-color: #29b6f6;` (Highlight color for active elements, links, and primary buttons)
- ` --wio-border-color: #424242;` (Standard border color)

### Typography

- ` --wio-font-size-sm: 11px;`
- ` --wio-font-size-md: 13px;`
- ` --wio-font-size-lg: 15px;`

### Spacing

- ` --wio-spacing-xs: 2px;`
- ` --wio-spacing-sm: 6px;`
- ` --wio-spacing-md: 10px;`
- ` --wio-spacing-lg: 14px;`

### Style Primitives

- ` --wio-border-radius: 4px;` (Standard border radius for all components)
- ` --wio-shadow: none;` (Shadows are disabled to maintain the flat design)

## 4. Component Notes

- **Modals & Toasts**: The UI uses the `SweetAlert2` library for all modal and toast notifications. Custom styles are applied via CSS overrides in `ui.ts` to match the dark theme. All calls should be made through the helper functions in `ui/modals.ts` and `ui/notifications.ts` to ensure a consistent look and feel.
- **Vue Components**: Example Vue components (`.vue` files) use scoped styles. To maintain consistency, manually use the color and spacing values defined in this guide.
]]>