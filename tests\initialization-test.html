<![CDATA[
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIO Initialization Test</title>
    <style>
        body { font-family: sans-serif; padding: 20px; }
        #status { padding: 10px; border-radius: 5px; font-weight: bold; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .failure { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        #test-area { border: 2px dashed #ccc; padding: 20px; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>World Info Optimizer (WIO) Initialization Test</h1>
    <p>This test simulates the SillyTavern environment to verify that the WIO activation button is correctly injected.</p>
    
    <h2>Test Result</h2>
    <div id="status">Running...</div>

    <div id="test-area">
        <h2>Simulated Tavern UI</h2>
        <p>This is the target element where the button should appear:</p>
        <div id="extensionsMenu" style="border: 1px solid #007bff; padding: 10px; min-height: 50px;">
            <!-- WIO button should be injected here -->
        </div>
    </div>

    <script>
        // --- Test Setup: Mocking the Tavern Environment ---

        // 1. Mock window.parent to point to the current window
        window.parent = window;

        // 2. Mock essential Tavern APIs
        window.TavernHelper = {
            getCharData: () => ({ name: 'Test Character' }),
        };

        // 3. Mock jQuery
        window.jQuery = document.querySelector.bind(document);
        window.jQuery.ajax = () => {}; // No-op for any network requests
        
        // --- Test Execution ---
        
        console.log('[Test Runner] Mock environment is ready. Loading script...');

        const script = document.createElement('script');
        // IMPORTANT: This test assumes the script has been built to the /dist folder.
        // Run `npm run build` or `pnpm build` before opening this file.
        script.src = '../dist/world_info_optimizer/index.js';
        script.onerror = () => {
            console.error('[Test Runner] Failed to load the script. Make sure you have run the build command.');
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = 'TEST FAILED: Script not found. Did you run "pnpm build"?';
            statusDiv.className = 'failure';
        };
        document.body.appendChild(script);

        // --- Test Assertion ---

        setTimeout(() => {
            console.log('[Test Runner] Running assertion...');
            const statusDiv = document.getElementById('status');
            const extensionsMenu = document.getElementById('extensionsMenu');
            
            // The button's ID is defined in `constants.ts` as 'wio-launcher-button'
            const wioButton = extensionsMenu.querySelector('#wio-launcher-button');

            if (wioButton && wioButton.textContent.includes('WIO Manager')) {
                console.log('[Test Runner] SUCCESS: Button found inside #extensionsMenu.');
                statusDiv.textContent = 'TEST PASSED: The WIO activation button was successfully injected into the extensions menu.';
                statusDiv.className = 'success';
            } else {
                console.error('[Test Runner] FAILURE: Button not found or has incorrect content.');
                statusDiv.textContent = 'TEST FAILED: The WIO activation button was NOT found inside the #extensionsMenu element.';
                statusDiv.className = 'failure';
            }
        }, 1000); // Wait 1 second for the script's readiness check to complete

    </script>
</body>
</html>
]]>