var __webpack_modules__ = {
  "./src/world_info_optimizer/api.ts": 
  /*!*****************************************!*\
  !*** ./src/world_info_optimizer/api.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TavernAPI: () => (/* binding */ TavernAPI)\n/* harmony export */ });\n/* harmony import */ var _ui_modals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui/modals */ \"./src/world_info_optimizer/ui/modals.ts\");\n// src/world_info_optimizer/api.ts\n\n/**\n * 错误处理装饰器或高阶函数，用于封装API调用。\n * @param fn 要封装的异步函数\n * @param context 错误日志的上下文\n */\nconst errorCatched = (fn, context = 'TavernAPI') => {\n    return async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            // 用户取消操作（例如在模态框中点击取消）通常会抛出null或undefined\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                // 可以在这里决定是否显示一个全局的错误提示\n                await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_0__.showModal)({\n                    type: 'alert',\n                    title: 'API调用异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n            return null;\n        }\n    };\n};\n/**\n * 获取全局TavernHelper实例。\n * @returns {TavernHelper}\n * @throws 如果TavernHelper未定义，则抛出错误。\n */\nconst getTavernHelper = () => {\n    const parentWin = window.parent;\n    if (!parentWin.TavernHelper) {\n        throw new Error('TavernHelper is not available on the parent window.');\n    }\n    return parentWin.TavernHelper;\n};\nconst getSillyTavernContext = () => {\n    const parentWin = window.parent;\n    if (!parentWin.SillyTavern || typeof parentWin.SillyTavern.getContext !== 'function') {\n        // 在这种情况下，我们不应该抛出错误，而应该返回一个默认的、无害的上下文。\n        // 因为脚本可能在SillyTavern完全加载之前运行。\n        console.warn('[WIO API] SillyTavern.getContext is not available.');\n        return {\n            characters: [],\n            characterId: null,\n            chatId: null,\n        };\n    }\n    return parentWin.SillyTavern.getContext();\n};\n// API封装模块\nclass TavernAPIWrapper {\n    helper;\n    constructor() {\n        this.helper = getTavernHelper();\n    }\n    // Lorebook APIs\n    createLorebook = errorCatched(async (name) => this.helper.createLorebook(name));\n    deleteLorebook = errorCatched(async (name) => this.helper.deleteLorebook(name));\n    getLorebooks = errorCatched(async () => this.helper.getLorebooks());\n    getLorebookSettings = errorCatched(async () => this.helper.getLorebookSettings());\n    setLorebookSettings = errorCatched(async (settings) => this.helper.setLorebookSettings(settings));\n    // Lorebook Entry APIs\n    getLorebookEntries = errorCatched(async (name) => this.helper.getLorebookEntries(name));\n    setLorebookEntries = errorCatched(async (name, entries) => this.helper.setLorebookEntries(name, entries));\n    createLorebookEntries = errorCatched(async (name, entries) => this.helper.createLorebookEntries(name, entries));\n    deleteLorebookEntries = errorCatched(async (name, uids) => this.helper.deleteLorebookEntries(name, uids));\n    // Character-specific Lorebook APIs\n    getCharLorebooks = errorCatched(async (charData) => this.helper.getCharLorebooks(charData));\n    getCurrentCharLorebooks = errorCatched(async () => this.helper.getCharLorebooks());\n    setCurrentCharLorebooks = errorCatched(async (lorebooks) => this.helper.setCurrentCharLorebooks(lorebooks));\n    // Chat Lorebook APIs\n    getChatLorebook = errorCatched(async () => this.helper.getChatLorebook());\n    setChatLorebook = errorCatched(async (name) => this.helper.setChatLorebook(name));\n    getOrCreateChatLorebook = errorCatched(async (name) => this.helper.getOrCreateChatLorebook(name));\n    // Regex APIs\n    getRegexes = errorCatched(async () => this.helper.getTavernRegexes({ scope: 'all' }));\n    replaceRegexes = errorCatched(async (regexes) => this.helper.replaceTavernRegexes(regexes, { scope: 'all' }));\n    // Character Data APIs\n    getCharData = errorCatched(async () => this.helper.getCharData());\n    // Misc APIs\n    saveSettings = errorCatched(async () => this.helper.builtin.saveSettings());\n    // Context API\n    getContext = () => getSillyTavernContext();\n    // Direct access to specific properties if needed\n    get Character() {\n        return this.helper.Character;\n    }\n}\n// 导出单例\nconst TavernAPI = new TavernAPIWrapper();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsa0NBQWtDO0FBR007QUFFeEM7Ozs7R0FJRztBQUNILE1BQU0sWUFBWSxHQUFHLENBQTZDLEVBQUssRUFBRSxVQUFrQixXQUFXLEVBQUUsRUFBRTtJQUN4RyxPQUFPLEtBQUssRUFBRSxHQUFHLElBQW1CLEVBQWlDLEVBQUU7UUFDckUsSUFBSSxDQUFDO1lBQ0gsT0FBTyxNQUFNLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDO1FBQzNCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YseUNBQXlDO1lBQ3pDLElBQUksS0FBSyxFQUFFLENBQUM7Z0JBQ1YsT0FBTyxDQUFDLEtBQUssQ0FBQyxJQUFJLE9BQU8sVUFBVSxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUM1Qyx1QkFBdUI7Z0JBQ3ZCLE1BQU0scURBQVMsQ0FBQztvQkFDZCxJQUFJLEVBQUUsT0FBTztvQkFDYixLQUFLLEVBQUUsU0FBUztvQkFDaEIsSUFBSSxFQUFFLDRCQUE0QjtpQkFDbkMsQ0FBQyxDQUFDO1lBQ0wsQ0FBQztZQUNELE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUMsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGOzs7O0dBSUc7QUFDSCxNQUFNLGVBQWUsR0FBRyxHQUFpQixFQUFFO0lBQ3pDLE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxNQUFhLENBQUM7SUFDdkMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLEVBQUUsQ0FBQztRQUM1QixNQUFNLElBQUksS0FBSyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7SUFDekUsQ0FBQztJQUNELE9BQU8sU0FBUyxDQUFDLFlBQVksQ0FBQztBQUNoQyxDQUFDLENBQUM7QUFFRixNQUFNLHFCQUFxQixHQUFHLEdBQUcsRUFBRTtJQUNqQyxNQUFNLFNBQVMsR0FBRyxNQUFNLENBQUMsTUFBYSxDQUFDO0lBQ3ZDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxJQUFJLE9BQU8sU0FBUyxDQUFDLFdBQVcsQ0FBQyxVQUFVLEtBQUssVUFBVSxFQUFFLENBQUM7UUFDckYsc0NBQXNDO1FBQ3RDLDhCQUE4QjtRQUM5QixPQUFPLENBQUMsSUFBSSxDQUFDLG9EQUFvRCxDQUFDLENBQUM7UUFDbkUsT0FBTztZQUNMLFVBQVUsRUFBRSxFQUFFO1lBQ2QsV0FBVyxFQUFFLElBQUk7WUFDakIsTUFBTSxFQUFFLElBQUk7U0FDYixDQUFDO0lBQ0osQ0FBQztJQUNELE9BQU8sU0FBUyxDQUFDLFdBQVcsQ0FBQyxVQUFVLEVBQUUsQ0FBQztBQUM1QyxDQUFDLENBQUM7QUFFRixVQUFVO0FBQ1YsTUFBTSxnQkFBZ0I7SUFDWixNQUFNLENBQWU7SUFFN0I7UUFDRSxJQUFJLENBQUMsTUFBTSxHQUFHLGVBQWUsRUFBRSxDQUFDO0lBQ2xDLENBQUM7SUFFRCxnQkFBZ0I7SUFDaEIsY0FBYyxHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsSUFBWSxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0lBQ3hGLGNBQWMsR0FBRyxZQUFZLENBQUMsS0FBSyxFQUFFLElBQVksRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUN4RixZQUFZLEdBQUcsWUFBWSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO0lBQ3BFLG1CQUFtQixHQUFHLFlBQVksQ0FBQyxLQUFLLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsbUJBQW1CLEVBQUUsQ0FBQyxDQUFDO0lBQ2xGLG1CQUFtQixHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsUUFBYSxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFFdkcsc0JBQXNCO0lBQ3RCLGtCQUFrQixHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsSUFBWSxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDaEcsa0JBQWtCLEdBQUcsWUFBWSxDQUFDLEtBQUssRUFBRSxJQUFZLEVBQUUsT0FBYyxFQUFFLEVBQUUsQ0FDdkUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQzlDLENBQUM7SUFDRixxQkFBcUIsR0FBRyxZQUFZLENBQUMsS0FBSyxFQUFFLElBQVksRUFBRSxPQUFjLEVBQUUsRUFBRSxDQUMxRSxJQUFJLENBQUMsTUFBTSxDQUFDLHFCQUFxQixDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FDakQsQ0FBQztJQUNGLHFCQUFxQixHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsSUFBWSxFQUFFLElBQWMsRUFBRSxFQUFFLENBQzFFLElBQUksQ0FBQyxNQUFNLENBQUMscUJBQXFCLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUM5QyxDQUFDO0lBRUYsbUNBQW1DO0lBQ25DLGdCQUFnQixHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsUUFBYyxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFDbEcsdUJBQXVCLEdBQUcsWUFBWSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDLENBQUM7SUFDbkYsdUJBQXVCLEdBQUcsWUFBWSxDQUFDLEtBQUssRUFBRSxTQUFtQixFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLHVCQUF1QixDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUM7SUFFdEgscUJBQXFCO0lBQ3JCLGVBQWUsR0FBRyxZQUFZLENBQUMsS0FBSyxJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLGVBQWUsRUFBRSxDQUFDLENBQUM7SUFDMUUsZUFBZSxHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsSUFBbUIsRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUNqRyx1QkFBdUIsR0FBRyxZQUFZLENBQUMsS0FBSyxFQUFFLElBQVksRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyx1QkFBdUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0lBRTFHLGFBQWE7SUFDYixVQUFVLEdBQUcsWUFBWSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDdEYsY0FBYyxHQUFHLFlBQVksQ0FBQyxLQUFLLEVBQUUsT0FBYyxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLE9BQU8sRUFBRSxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFFckgsc0JBQXNCO0lBQ3RCLFdBQVcsR0FBRyxZQUFZLENBQUMsS0FBSyxJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUM7SUFFbEUsWUFBWTtJQUNaLFlBQVksR0FBRyxZQUFZLENBQUMsS0FBSyxJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO0lBRTVFLGNBQWM7SUFDZCxVQUFVLEdBQUcsR0FBRyxFQUFFLENBQUMscUJBQXFCLEVBQUUsQ0FBQztJQUUzQyxpREFBaUQ7SUFDakQsSUFBSSxTQUFTO1FBQ1gsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQztJQUMvQixDQUFDO0NBQ0Y7QUFFRCxPQUFPO0FBQ0EsTUFBTSxTQUFTLEdBQUcsSUFBSSxnQkFBZ0IsRUFBRSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy93b3JsZF9pbmZvX29wdGltaXplci9hcGkudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy93b3JsZF9pbmZvX29wdGltaXplci9hcGkudHNcblxuaW1wb3J0IHsgVGF2ZXJuSGVscGVyIH0gZnJvbSAnLi90YXZlcm4taGVscGVyLXR5cGVzJztcbmltcG9ydCB7IHNob3dNb2RhbCB9IGZyb20gJy4vdWkvbW9kYWxzJztcblxuLyoqXG4gKiDplJnor6/lpITnkIboo4XppbDlmajmiJbpq5jpmLblh73mlbDvvIznlKjkuo7lsIHoo4VBUEnosIPnlKjjgIJcbiAqIEBwYXJhbSBmbiDopoHlsIHoo4XnmoTlvILmraXlh73mlbBcbiAqIEBwYXJhbSBjb250ZXh0IOmUmeivr+aXpeW/l+eahOS4iuS4i+aWh1xuICovXG5jb25zdCBlcnJvckNhdGNoZWQgPSA8VCBleHRlbmRzICguLi5hcmdzOiBhbnlbXSkgPT4gUHJvbWlzZTxhbnk+PihmbjogVCwgY29udGV4dDogc3RyaW5nID0gJ1RhdmVybkFQSScpID0+IHtcbiAgcmV0dXJuIGFzeW5jICguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KTogUHJvbWlzZTxSZXR1cm5UeXBlPFQ+IHwgbnVsbD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gYXdhaXQgZm4oLi4uYXJncyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIOeUqOaIt+WPlua2iOaTjeS9nO+8iOS+i+WmguWcqOaooeaAgeahhuS4reeCueWHu+WPlua2iO+8iemAmuW4uOS8muaKm+WHum51bGzmiJZ1bmRlZmluZWRcbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbJHtjb250ZXh0fV0gRXJyb3I6YCwgZXJyb3IpO1xuICAgICAgICAvLyDlj6/ku6XlnKjov5nph4zlhrPlrprmmK/lkKbmmL7npLrkuIDkuKrlhajlsYDnmoTplJnor6/mj5DnpLpcbiAgICAgICAgYXdhaXQgc2hvd01vZGFsKHtcbiAgICAgICAgICB0eXBlOiAnYWxlcnQnLFxuICAgICAgICAgIHRpdGxlOiAnQVBJ6LCD55So5byC5bi4JyxcbiAgICAgICAgICB0ZXh0OiBg5pON5L2c5Lit5Y+R55Sf5pyq55+l6ZSZ6K+v77yM6K+35qOA5p+l5byA5Y+R6ICF5o6n5Yi25Y+w6I635Y+W6K+m57uG5L+h5oGv44CCYCxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG59O1xuXG4vKipcbiAqIOiOt+WPluWFqOWxgFRhdmVybkhlbHBlcuWunuS+i+OAglxuICogQHJldHVybnMge1RhdmVybkhlbHBlcn1cbiAqIEB0aHJvd3Mg5aaC5p6cVGF2ZXJuSGVscGVy5pyq5a6a5LmJ77yM5YiZ5oqb5Ye66ZSZ6K+v44CCXG4gKi9cbmNvbnN0IGdldFRhdmVybkhlbHBlciA9ICgpOiBUYXZlcm5IZWxwZXIgPT4ge1xuICBjb25zdCBwYXJlbnRXaW4gPSB3aW5kb3cucGFyZW50IGFzIGFueTtcbiAgaWYgKCFwYXJlbnRXaW4uVGF2ZXJuSGVscGVyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdUYXZlcm5IZWxwZXIgaXMgbm90IGF2YWlsYWJsZSBvbiB0aGUgcGFyZW50IHdpbmRvdy4nKTtcbiAgfVxuICByZXR1cm4gcGFyZW50V2luLlRhdmVybkhlbHBlcjtcbn07XG5cbmNvbnN0IGdldFNpbGx5VGF2ZXJuQ29udGV4dCA9ICgpID0+IHtcbiAgY29uc3QgcGFyZW50V2luID0gd2luZG93LnBhcmVudCBhcyBhbnk7XG4gIGlmICghcGFyZW50V2luLlNpbGx5VGF2ZXJuIHx8IHR5cGVvZiBwYXJlbnRXaW4uU2lsbHlUYXZlcm4uZ2V0Q29udGV4dCAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIC8vIOWcqOi/meenjeaDheWGteS4i++8jOaIkeS7rOS4jeW6lOivpeaKm+WHuumUmeivr++8jOiAjOW6lOivpei/lOWbnuS4gOS4qum7mOiupOeahOOAgeaXoOWus+eahOS4iuS4i+aWh+OAglxuICAgIC8vIOWboOS4uuiEmuacrOWPr+iDveWcqFNpbGx5VGF2ZXJu5a6M5YWo5Yqg6L295LmL5YmN6L+Q6KGM44CCXG4gICAgY29uc29sZS53YXJuKCdbV0lPIEFQSV0gU2lsbHlUYXZlcm4uZ2V0Q29udGV4dCBpcyBub3QgYXZhaWxhYmxlLicpO1xuICAgIHJldHVybiB7XG4gICAgICBjaGFyYWN0ZXJzOiBbXSxcbiAgICAgIGNoYXJhY3RlcklkOiBudWxsLFxuICAgICAgY2hhdElkOiBudWxsLFxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHBhcmVudFdpbi5TaWxseVRhdmVybi5nZXRDb250ZXh0KCk7XG59O1xuXG4vLyBBUEnlsIHoo4XmqKHlnZdcbmNsYXNzIFRhdmVybkFQSVdyYXBwZXIge1xuICBwcml2YXRlIGhlbHBlcjogVGF2ZXJuSGVscGVyO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuaGVscGVyID0gZ2V0VGF2ZXJuSGVscGVyKCk7XG4gIH1cblxuICAvLyBMb3JlYm9vayBBUElzXG4gIGNyZWF0ZUxvcmVib29rID0gZXJyb3JDYXRjaGVkKGFzeW5jIChuYW1lOiBzdHJpbmcpID0+IHRoaXMuaGVscGVyLmNyZWF0ZUxvcmVib29rKG5hbWUpKTtcbiAgZGVsZXRlTG9yZWJvb2sgPSBlcnJvckNhdGNoZWQoYXN5bmMgKG5hbWU6IHN0cmluZykgPT4gdGhpcy5oZWxwZXIuZGVsZXRlTG9yZWJvb2sobmFtZSkpO1xuICBnZXRMb3JlYm9va3MgPSBlcnJvckNhdGNoZWQoYXN5bmMgKCkgPT4gdGhpcy5oZWxwZXIuZ2V0TG9yZWJvb2tzKCkpO1xuICBnZXRMb3JlYm9va1NldHRpbmdzID0gZXJyb3JDYXRjaGVkKGFzeW5jICgpID0+IHRoaXMuaGVscGVyLmdldExvcmVib29rU2V0dGluZ3MoKSk7XG4gIHNldExvcmVib29rU2V0dGluZ3MgPSBlcnJvckNhdGNoZWQoYXN5bmMgKHNldHRpbmdzOiBhbnkpID0+IHRoaXMuaGVscGVyLnNldExvcmVib29rU2V0dGluZ3Moc2V0dGluZ3MpKTtcblxuICAvLyBMb3JlYm9vayBFbnRyeSBBUElzXG4gIGdldExvcmVib29rRW50cmllcyA9IGVycm9yQ2F0Y2hlZChhc3luYyAobmFtZTogc3RyaW5nKSA9PiB0aGlzLmhlbHBlci5nZXRMb3JlYm9va0VudHJpZXMobmFtZSkpO1xuICBzZXRMb3JlYm9va0VudHJpZXMgPSBlcnJvckNhdGNoZWQoYXN5bmMgKG5hbWU6IHN0cmluZywgZW50cmllczogYW55W10pID0+XG4gICAgdGhpcy5oZWxwZXIuc2V0TG9yZWJvb2tFbnRyaWVzKG5hbWUsIGVudHJpZXMpLFxuICApO1xuICBjcmVhdGVMb3JlYm9va0VudHJpZXMgPSBlcnJvckNhdGNoZWQoYXN5bmMgKG5hbWU6IHN0cmluZywgZW50cmllczogYW55W10pID0+XG4gICAgdGhpcy5oZWxwZXIuY3JlYXRlTG9yZWJvb2tFbnRyaWVzKG5hbWUsIGVudHJpZXMpLFxuICApO1xuICBkZWxldGVMb3JlYm9va0VudHJpZXMgPSBlcnJvckNhdGNoZWQoYXN5bmMgKG5hbWU6IHN0cmluZywgdWlkczogc3RyaW5nW10pID0+XG4gICAgdGhpcy5oZWxwZXIuZGVsZXRlTG9yZWJvb2tFbnRyaWVzKG5hbWUsIHVpZHMpLFxuICApO1xuXG4gIC8vIENoYXJhY3Rlci1zcGVjaWZpYyBMb3JlYm9vayBBUElzXG4gIGdldENoYXJMb3JlYm9va3MgPSBlcnJvckNhdGNoZWQoYXN5bmMgKGNoYXJEYXRhPzogYW55KSA9PiB0aGlzLmhlbHBlci5nZXRDaGFyTG9yZWJvb2tzKGNoYXJEYXRhKSk7XG4gIGdldEN1cnJlbnRDaGFyTG9yZWJvb2tzID0gZXJyb3JDYXRjaGVkKGFzeW5jICgpID0+IHRoaXMuaGVscGVyLmdldENoYXJMb3JlYm9va3MoKSk7XG4gIHNldEN1cnJlbnRDaGFyTG9yZWJvb2tzID0gZXJyb3JDYXRjaGVkKGFzeW5jIChsb3JlYm9va3M6IHN0cmluZ1tdKSA9PiB0aGlzLmhlbHBlci5zZXRDdXJyZW50Q2hhckxvcmVib29rcyhsb3JlYm9va3MpKTtcblxuICAvLyBDaGF0IExvcmVib29rIEFQSXNcbiAgZ2V0Q2hhdExvcmVib29rID0gZXJyb3JDYXRjaGVkKGFzeW5jICgpID0+IHRoaXMuaGVscGVyLmdldENoYXRMb3JlYm9vaygpKTtcbiAgc2V0Q2hhdExvcmVib29rID0gZXJyb3JDYXRjaGVkKGFzeW5jIChuYW1lOiBzdHJpbmcgfCBudWxsKSA9PiB0aGlzLmhlbHBlci5zZXRDaGF0TG9yZWJvb2sobmFtZSkpO1xuICBnZXRPckNyZWF0ZUNoYXRMb3JlYm9vayA9IGVycm9yQ2F0Y2hlZChhc3luYyAobmFtZTogc3RyaW5nKSA9PiB0aGlzLmhlbHBlci5nZXRPckNyZWF0ZUNoYXRMb3JlYm9vayhuYW1lKSk7XG5cbiAgLy8gUmVnZXggQVBJc1xuICBnZXRSZWdleGVzID0gZXJyb3JDYXRjaGVkKGFzeW5jICgpID0+IHRoaXMuaGVscGVyLmdldFRhdmVyblJlZ2V4ZXMoeyBzY29wZTogJ2FsbCcgfSkpO1xuICByZXBsYWNlUmVnZXhlcyA9IGVycm9yQ2F0Y2hlZChhc3luYyAocmVnZXhlczogYW55W10pID0+IHRoaXMuaGVscGVyLnJlcGxhY2VUYXZlcm5SZWdleGVzKHJlZ2V4ZXMsIHsgc2NvcGU6ICdhbGwnIH0pKTtcblxuICAvLyBDaGFyYWN0ZXIgRGF0YSBBUElzXG4gIGdldENoYXJEYXRhID0gZXJyb3JDYXRjaGVkKGFzeW5jICgpID0+IHRoaXMuaGVscGVyLmdldENoYXJEYXRhKCkpO1xuXG4gIC8vIE1pc2MgQVBJc1xuICBzYXZlU2V0dGluZ3MgPSBlcnJvckNhdGNoZWQoYXN5bmMgKCkgPT4gdGhpcy5oZWxwZXIuYnVpbHRpbi5zYXZlU2V0dGluZ3MoKSk7XG5cbiAgLy8gQ29udGV4dCBBUElcbiAgZ2V0Q29udGV4dCA9ICgpID0+IGdldFNpbGx5VGF2ZXJuQ29udGV4dCgpO1xuXG4gIC8vIERpcmVjdCBhY2Nlc3MgdG8gc3BlY2lmaWMgcHJvcGVydGllcyBpZiBuZWVkZWRcbiAgZ2V0IENoYXJhY3RlcigpIHtcbiAgICByZXR1cm4gdGhpcy5oZWxwZXIuQ2hhcmFjdGVyO1xuICB9XG59XG5cbi8vIOWvvOWHuuWNleS+i1xuZXhwb3J0IGNvbnN0IFRhdmVybkFQSSA9IG5ldyBUYXZlcm5BUElXcmFwcGVyKCk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/api.ts\n\n}");
  },
  "./src/world_info_optimizer/constants.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/constants.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BULK_DELETE_BTN_ID: () => (/* binding */ BULK_DELETE_BTN_ID),\n/* harmony export */   BULK_DISABLE_BTN_ID: () => (/* binding */ BULK_DISABLE_BTN_ID),\n/* harmony export */   BULK_ENABLE_BTN_ID: () => (/* binding */ BULK_ENABLE_BTN_ID),\n/* harmony export */   BULK_REPLACE_BTN_ID: () => (/* binding */ BULK_REPLACE_BTN_ID),\n/* harmony export */   BULK_REPLACE_INPUT_ID: () => (/* binding */ BULK_REPLACE_INPUT_ID),\n/* harmony export */   BULK_SEARCH_INPUT_ID: () => (/* binding */ BULK_SEARCH_INPUT_ID),\n/* harmony export */   BUTTON_ICON_URL: () => (/* binding */ BUTTON_ICON_URL),\n/* harmony export */   BUTTON_ID: () => (/* binding */ BUTTON_ID),\n/* harmony export */   BUTTON_TEXT_IN_MENU: () => (/* binding */ BUTTON_TEXT_IN_MENU),\n/* harmony export */   BUTTON_TOOLTIP: () => (/* binding */ BUTTON_TOOLTIP),\n/* harmony export */   COLLAPSE_ALL_BTN_ID: () => (/* binding */ COLLAPSE_ALL_BTN_ID),\n/* harmony export */   COLLAPSE_CURRENT_BTN_ID: () => (/* binding */ COLLAPSE_CURRENT_BTN_ID),\n/* harmony export */   CREATE_LOREBOOK_BTN_ID: () => (/* binding */ CREATE_LOREBOOK_BTN_ID),\n/* harmony export */   EXPAND_ALL_BTN_ID: () => (/* binding */ EXPAND_ALL_BTN_ID),\n/* harmony export */   INITIAL_ACTIVE_TAB: () => (/* binding */ INITIAL_ACTIVE_TAB),\n/* harmony export */   LOREBOOK_INSERTION_POSITIONS: () => (/* binding */ LOREBOOK_INSERTION_POSITIONS),\n/* harmony export */   LOREBOOK_LOGIC_OPTIONS: () => (/* binding */ LOREBOOK_LOGIC_OPTIONS),\n/* harmony export */   MULTI_SELECT_BTN_ID: () => (/* binding */ MULTI_SELECT_BTN_ID),\n/* harmony export */   PANEL_ID: () => (/* binding */ PANEL_ID),\n/* harmony export */   REFRESH_BTN_ID: () => (/* binding */ REFRESH_BTN_ID),\n/* harmony export */   SEARCH_INPUT_ID: () => (/* binding */ SEARCH_INPUT_ID)\n/* harmony export */ });\n// src/world_info_optimizer/constants.ts\n// --- UI Element IDs ---\nconst PANEL_ID = 'world-info-optimizer-panel';\nconst BUTTON_ID = 'world-info-optimizer-button';\nconst SEARCH_INPUT_ID = 'wio-search-input';\nconst REFRESH_BTN_ID = 'wio-refresh-btn';\nconst COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\nconst COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\nconst CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n// --- Bulk Operations ---\nconst BULK_SEARCH_INPUT_ID = 'wio-bulk-search-input';\nconst BULK_REPLACE_INPUT_ID = 'wio-bulk-replace-input';\nconst BULK_REPLACE_BTN_ID = 'wio-bulk-replace-btn';\n// --- Multi-Select Operations ---\nconst MULTI_SELECT_BTN_ID = 'wio-multi-select-btn';\nconst BULK_DELETE_BTN_ID = 'wio-bulk-delete-btn';\nconst BULK_ENABLE_BTN_ID = 'wio-bulk-enable-btn';\nconst BULK_DISABLE_BTN_ID = 'wio-bulk-disable-btn';\n// --- Collapse/Expand Operations ---\nconst EXPAND_ALL_BTN_ID = 'wio-expand-all-btn';\n// --- UI Display Text & URLs ---\nconst BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\nconst BUTTON_TOOLTIP = '世界书 & 正则便捷管理 (WIO)';\nconst BUTTON_TEXT_IN_MENU = '世界书 & 正则便捷管理 (WIO)';\n// --- Lorebook Configuration Options ---\nconst LOREBOOK_INSERTION_POSITIONS = {\n    before_character_definition: '角色定义前',\n    after_character_definition: '角色定义后',\n    before_example_messages: '聊天示例前',\n    after_example_messages: '聊天示例后',\n    before_author_note: '作者笔记前',\n    after_author_note: '作者笔记后',\n    at_depth_as_system: '@D ⚙ 系统',\n    at_depth_as_assistant: '@D 🗨️ 角色',\n    at_depth_as_user: '@D 👤 用户',\n};\nconst LOREBOOK_LOGIC_OPTIONS = {\n    and_any: '任一 AND',\n    and_all: '所有 AND',\n    not_any: '任一 NOT',\n    not_all: '所有 NOT',\n};\n// --- Other Constants ---\nconst INITIAL_ACTIVE_TAB = 'global-lore';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/constants.ts\n\n}");
  },
  "./src/world_info_optimizer/core.ts": 
  /*!******************************************!*\
  !*** ./src/world_info_optimizer/core.ts ***!
  \******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLorebook: () => (/* binding */ createLorebook),\n/* harmony export */   createLorebookEntry: () => (/* binding */ createLorebookEntry),\n/* harmony export */   createRegex: () => (/* binding */ createRegex),\n/* harmony export */   deleteLorebook: () => (/* binding */ deleteLorebook),\n/* harmony export */   deleteLorebookEntry: () => (/* binding */ deleteLorebookEntry),\n/* harmony export */   deleteRegex: () => (/* binding */ deleteRegex),\n/* harmony export */   loadAllData: () => (/* binding */ loadAllData),\n/* harmony export */   performBulkDelete: () => (/* binding */ performBulkDelete),\n/* harmony export */   performBulkDisable: () => (/* binding */ performBulkDisable),\n/* harmony export */   performBulkEnable: () => (/* binding */ performBulkEnable),\n/* harmony export */   performBulkReplace: () => (/* binding */ performBulkReplace),\n/* harmony export */   renameLorebook: () => (/* binding */ renameLorebook),\n/* harmony export */   setGlobalLorebookEnabled: () => (/* binding */ setGlobalLorebookEnabled),\n/* harmony export */   updateLorebookEntry: () => (/* binding */ updateLorebookEntry),\n/* harmony export */   updateRegex: () => (/* binding */ updateRegex)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"./src/world_info_optimizer/api.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n// src/world_info_optimizer/core.ts\n\n\n/**\n * 加载所有初始数据。\n * 这是应用启动时调用的主要数据获取函数。\n */\nconst loadAllData = async () => {\n    const currentState = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    // 防止并发加载\n    if (currentState.isLoading) {\n        console.log('[WIO Core] Data loading already in progress, skipping...');\n        return;\n    }\n    // 开始加载\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n        ...s,\n        isDataLoaded: false,\n        isLoading: true,\n        loadError: null,\n    }));\n    try {\n        const context = _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getContext();\n        const { characters: allCharacters, characterId, chatId } = context;\n        const hasActiveCharacter = characterId !== undefined && characterId !== null;\n        const hasActiveChat = chatId !== undefined && chatId !== null;\n        // --- 并行获取基础数据 ---\n        const results = await Promise.allSettled([\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getRegexes(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebooks(),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharData() : Promise.resolve(null),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),\n            hasActiveChat ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getChatLorebook() : Promise.resolve(null),\n        ]);\n        // --- 安全地从Promise结果中提取数据 ---\n        const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n        const charData = results[3].status === 'fulfilled' ? results[3].value : null;\n        const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : null;\n        const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;\n        // --- 数据处理 ---\n        // 1. 正则表达式处理\n        const globalRegexes = (allUIRegexes || []).filter(r => r.scope === 'global');\n        const characterRegexes = processCharacterRegexes(allUIRegexes, charData);\n        // 2. 世界书文件和启用状态\n        const enabledGlobalBooks = new Set(globalSettings?.selected_global_lorebooks || []);\n        const allLorebooks = (allBookFileNames || []).map(name => ({\n            name: name,\n            enabled: enabledGlobalBooks.has(name),\n        }));\n        // 3. 当前角色关联的世界书\n        const charBookSet = new Set();\n        if (charLinkedBooks?.primary)\n            charBookSet.add(charLinkedBooks.primary);\n        if (charLinkedBooks?.additional)\n            charLinkedBooks.additional.forEach(b => charBookSet.add(b));\n        const characterLorebooks = Array.from(charBookSet);\n        // 4. 计算所有角色对世界书的使用情况 (lorebookUsage)\n        const lorebookUsage = new Map();\n        const knownBookNames = new Set(allBookFileNames || []);\n        if (Array.isArray(allCharacters)) {\n            for (const char of allCharacters) {\n                if (!char?.name)\n                    continue;\n                // 注意: getCharLorebooks 是同步还是异步取决于TavernHelper的实现\n                // 为保险起见，我们假设它可能返回一个Promise\n                const books = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharLorebooks({ name: char.name });\n                const charBooks = new Set();\n                if (books?.primary)\n                    charBooks.add(books.primary);\n                if (books?.additional)\n                    books.additional.forEach(b => charBooks.add(b));\n                charBooks.forEach(bookName => {\n                    if (!lorebookUsage.has(bookName)) {\n                        lorebookUsage.set(bookName, []);\n                    }\n                    lorebookUsage.get(bookName)?.push(char.name);\n                    knownBookNames.add(bookName); // 确保所有被使用的书都被加载\n                });\n            }\n        }\n        // 5. 汇总所有需要加载条目的世界书\n        characterLorebooks.forEach(b => knownBookNames.add(b));\n        if (chatLorebook) {\n            knownBookNames.add(chatLorebook);\n        }\n        // 6. 并行加载所有世界书的条目\n        const lorebookEntries = new Map();\n        const entryPromises = Array.from(knownBookNames).map(async (name) => {\n            const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(name);\n            if (entries) {\n                lorebookEntries.set(name, entries);\n            }\n        });\n        await Promise.all(entryPromises);\n        // 7. 更新全局状态\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.setAllData)({\n            globalRegexes,\n            characterRegexes,\n            allLorebooks,\n            characterLorebooks,\n            chatLorebook: chatLorebook || null,\n            lorebookEntries,\n            lorebookUsage,\n        });\n    }\n    catch (error) {\n        console.error('[WIO Core] Failed to load all data:', error);\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n            ...s,\n            isDataLoaded: false, // 确保清除旧数据\n            isLoading: false,\n            loadError: `数据加载失败: ${errorMessage}`,\n        }));\n    }\n    finally {\n        // 确保加载状态总是被重置\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, isLoading: false }));\n    }\n};\n/**\n * 处理和合并来自UI和角色卡中的角色特定正则表达式。\n * @param allUIRegexes 从Tavern API获取的所有UI正则\n * @param charData 当前角色的数据\n * @returns 合并和去重后的角色正则表达式数组\n */\nfunction processCharacterRegexes(allUIRegexes, charData) {\n    const characterUIRegexes = allUIRegexes?.filter(r => r.scope === 'character') || [];\n    let cardRegexes = [];\n    if (charData && _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character) {\n        try {\n            const character = new _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character(charData);\n            cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                id: r.id || `card-${Date.now()}-${i}`,\n                script_name: r.scriptName || '未命名卡内正则',\n                find_regex: r.findRegex,\n                replace_string: r.replaceString,\n                enabled: !r.disabled,\n                scope: 'character',\n                source: 'card',\n            }));\n        }\n        catch (e) {\n            console.warn(\"[WIO Core] Couldn't parse character card regex scripts:\", e);\n        }\n    }\n    // 去重逻辑：UI中的正则优先\n    const uiRegexIdentifiers = new Set(characterUIRegexes.map(r => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n    const uniqueCardRegexes = cardRegexes.filter(r => {\n        const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n        return !uiRegexIdentifiers.has(identifier);\n    });\n    return [...characterUIRegexes, ...uniqueCardRegexes];\n}\n// --- Lorebook CRUD Operations ---\n/**\n * 创建一个新的空世界书。\n * @param bookName 新世界书的名称\n */\nconst createLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(bookName);\n    if (result !== null) {\n        await loadAllData(); // 重新加载所有数据以确保状态同步\n    }\n};\n/**\n * 重命名一个世界书。\n * @param oldName 旧名称\n * @param newName 新名称\n */\nconst renameLorebook = async (oldName, newName) => {\n    // 1. 获取旧书的所有条目\n    const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(oldName);\n    if (entries === null) {\n        throw new Error(`Failed to get entries for lorebook: ${oldName}`);\n    }\n    // 2. 创建一个新书\n    const createResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(newName);\n    if (createResult === null) {\n        throw new Error(`Failed to create new lorebook: ${newName}`);\n    }\n    // 3. 将条目复制到新书\n    if (entries.length > 0) {\n        const setResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(newName, entries);\n        if (setResult === null) {\n            // 清理：如果条目设置失败，删除新建的书\n            await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(newName);\n            throw new Error(`Failed to set entries for new lorebook: ${newName}`);\n        }\n    }\n    // 4. 删除旧书\n    const deleteResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(oldName);\n    if (deleteResult === null) {\n        // 这不是一个关键错误，但应该记录下来\n        console.warn(`Failed to delete old lorebook \"${oldName}\" after renaming.`);\n    }\n    // 5. 刷新数据\n    await loadAllData();\n};\n/**\n * 删除一个世界书。\n * @param bookName 要删除的世界书的名称\n */\nconst deleteLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(bookName);\n    if (result !== null) {\n        await loadAllData();\n    }\n};\n// --- Lorebook Entry CRUD Operations ---\n/**\n * 在指定的世界书中创建一个新条目。\n * @param bookName 世界书名称\n * @param entryData 要创建的条目的数据\n */\nconst createLorebookEntry = async (bookName, entryData) => {\n    // Tavern API需要一个数组\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebookEntries(bookName, [entryData]);\n    if (result !== null && result.length > 0) {\n        const newState = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const newEntry = result[0]; // API会返回创建后的条目，包含UID\n        const currentEntries = newState.lorebookEntries.get(bookName) || [];\n        newState.lorebookEntries.set(bookName, [...currentEntries, newEntry]);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(newState.lorebookEntries) }));\n    }\n};\n/**\n * 更新世界书中的一个现有条目。\n * @param bookName 世界书名称\n * @param uid 要更新的条目的唯一ID\n * @param updates 要应用于条目的部分更新\n */\nconst updateLorebookEntry = async (bookName, uid, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const entries = state.lorebookEntries.get(bookName);\n    if (!entries) {\n        throw new Error(`[WIO Core] Book not found in state: ${bookName}`);\n    }\n    let entryUpdated = false;\n    const updatedEntries = entries.map(entry => {\n        if (entry.uid === uid) {\n            entryUpdated = true;\n            return { ...entry, ...updates };\n        }\n        return entry;\n    });\n    if (entryUpdated) {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, updatedEntries);\n        if (result !== null) {\n            state.lorebookEntries.set(bookName, updatedEntries);\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n        }\n    }\n};\n/**\n * 从世界书中删除一个条目。\n * @param bookName 世界书名称\n * @param uid 要删除的条目的唯一ID\n */\nconst deleteLorebookEntry = async (bookName, uid) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntries(bookName, [uid]);\n    if (result !== null) {\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const entries = state.lorebookEntries.get(bookName) || [];\n        const filteredEntries = entries.filter(entry => entry.uid !== uid);\n        state.lorebookEntries.set(bookName, filteredEntries);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n    }\n};\n// --- Global Settings ---\n/**\n * 设置一个全局世界书的启用状态。\n * @param bookName 世界书的名称\n * @param enabled true为启用, false为禁用\n */\nconst setGlobalLorebookEnabled = async (bookName, enabled) => {\n    const settings = (await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings()) || {};\n    if (!settings.selected_global_lorebooks) {\n        settings.selected_global_lorebooks = [];\n    }\n    const enabledBooks = new Set(settings.selected_global_lorebooks);\n    if (enabled) {\n        enabledBooks.add(bookName);\n    }\n    else {\n        enabledBooks.delete(bookName);\n    }\n    settings.selected_global_lorebooks = Array.from(enabledBooks);\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookSettings(settings);\n    if (result !== null) {\n        // 更新本地状态以立即反映UI变化\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const book = state.allLorebooks.find(b => b.name === bookName);\n        if (book) {\n            book.enabled = enabled;\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, allLorebooks: [...state.allLorebooks] }));\n        }\n    }\n};\n// --- Regex CRUD Operations ---\n/**\n * 提交正则表达式列表的更改。\n * @param updatedList 要提交的完整正则表达式列表 (仅限UI来源)\n */\nconst commitRegexChanges = async (updatedList) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.replaceRegexes(updatedList);\n    if (result !== null) {\n        // 成功后，重新加载所有数据以确保与后端完全同步\n        // 这是最简单可靠的方式，避免复杂的本地状态管理\n        await loadAllData();\n    }\n};\n/**\n * 创建一个新的正则表达式。\n * @param newRegexData 要创建的正则表达式的数据\n */\nconst createRegex = async (newRegexData) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    // 只处理UI来源的正则\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const finalNewRegex = {\n        id: `ui-${Date.now()}`,\n        enabled: true,\n        source: 'ui',\n        ...newRegexData,\n    };\n    await commitRegexChanges([...currentUIRegexes, finalNewRegex]);\n};\n/**\n * 更新一个现有的正则表达式。\n * @param regexId 要更新的正则表达式的ID\n * @param updates 要应用的更新\n */\nconst updateRegex = async (regexId, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const updatedList = currentUIRegexes.map(r => (r.id === regexId ? { ...r, ...updates } : r));\n    await commitRegexChanges(updatedList);\n};\n/**\n * 删除一个正则表达式。\n * @param regexId 要删除的正则表达式的ID\n */\nconst deleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const updatedList = currentUIRegexes.filter(r => r.id !== regexId);\n    await commitRegexChanges(updatedList);\n};\n// --- Bulk Operations ---\n/**\n * 执行批量搜索和替换操作。\n * @param searchTerm 要搜索的词语\n * @param replaceTerm 要替换成的词语\n * @returns 返回被修改的条目数量\n */\nconst performBulkReplace = async (searchTerm, replaceTerm) => {\n    if (!searchTerm.trim()) {\n        throw new Error('搜索词不能为空');\n    }\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    let modifiedCount = 0;\n    const modifiedBooks = new Map();\n    // 提前创建正则表达式以提高性能\n    const searchRegex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\n    // 遍历所有世界书和条目\n    for (const [bookName, entries] of state.lorebookEntries.entries()) {\n        const modifiedEntries = [];\n        let bookHasChanges = false;\n        for (const entry of entries) {\n            let entryModified = false;\n            const updatedEntry = { ...entry };\n            // 替换条目名称 (comment)\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(searchRegex, replaceTerm);\n                if (newComment !== updatedEntry.comment) {\n                    updatedEntry.comment = newComment;\n                    entryModified = true;\n                }\n            }\n            // 替换关键词\n            if (updatedEntry.keys && updatedEntry.keys.length > 0) {\n                const originalKeys = JSON.stringify(updatedEntry.keys);\n                updatedEntry.keys = updatedEntry.keys.map(key => key.replace(searchRegex, replaceTerm));\n                if (JSON.stringify(updatedEntry.keys) !== originalKeys) {\n                    entryModified = true;\n                }\n            }\n            // 替换内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(searchRegex, replaceTerm);\n                if (newContent !== updatedEntry.content) {\n                    updatedEntry.content = newContent;\n                    entryModified = true;\n                }\n            }\n            if (entryModified) {\n                modifiedCount++;\n                bookHasChanges = true;\n            }\n            modifiedEntries.push(updatedEntry);\n        }\n        if (bookHasChanges) {\n            modifiedBooks.set(bookName, modifiedEntries);\n        }\n    }\n    // 并行保存所有修改过的世界书\n    const updatePromises = Array.from(modifiedBooks.entries()).map(async ([bookName, entries]) => {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, entries);\n        if (result !== null) {\n            // 在 Promise 内部准备状态更新，但不直接修改\n            return () => state.lorebookEntries.set(bookName, entries);\n        }\n        return null;\n    });\n    const stateUpdaters = (await Promise.all(updatePromises)).filter(Boolean);\n    stateUpdaters.forEach(update => update());\n    if (modifiedBooks.size > 0) {\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n    }\n    return modifiedCount;\n};\n/**\n * 批量删除选中的条目。\n * @returns 返回删除的条目数量\n */\nconst performBulkDelete = async () => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const selectedItems = Array.from(state.selectedItems);\n    if (selectedItems.length === 0) {\n        throw new Error('没有选中任何条目');\n    }\n    let deletedCount = 0;\n    const booksToUpdate = new Map(); // bookName -> uids to delete\n    // 解析选中的条目\n    for (const itemId of selectedItems) {\n        const [bookName, uid] = itemId.split('/');\n        if (bookName && uid) {\n            if (!booksToUpdate.has(bookName)) {\n                booksToUpdate.set(bookName, []);\n            }\n            booksToUpdate.get(bookName)?.push(uid);\n        }\n    }\n    // 并行删除每个世界书中的条目\n    const deletePromises = Array.from(booksToUpdate.entries()).map(async ([bookName, uidsToDelete]) => {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntries(bookName, uidsToDelete);\n        if (result !== null) {\n            return { bookName, uidsToDelete, count: uidsToDelete.length };\n        }\n        return null;\n    });\n    const deleteResults = (await Promise.all(deletePromises)).filter(Boolean);\n    for (const result of deleteResults) {\n        if (!result)\n            continue;\n        deletedCount += result.count;\n        // 更新本地状态\n        const entries = state.lorebookEntries.get(result.bookName) || [];\n        const filteredEntries = entries.filter(entry => !result.uidsToDelete.includes(entry.uid));\n        state.lorebookEntries.set(result.bookName, filteredEntries);\n    }\n    // 清空选中状态\n    state.selectedItems.clear();\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n        ...s,\n        lorebookEntries: new Map(state.lorebookEntries),\n        selectedItems: new Set(),\n        multiSelectMode: false,\n    }));\n    return deletedCount;\n};\n/**\n * 批量启用选中的条目。\n * @returns 返回启用的条目数量\n */\nconst performBulkEnable = async () => {\n    return await performBulkToggle(true);\n};\n/**\n * 批量禁用选中的条目。\n * @returns 返回禁用的条目数量\n */\nconst performBulkDisable = async () => {\n    return await performBulkToggle(false);\n};\n/**\n * 批量切换条目的启用/禁用状态。\n * @param enabled 目标状态\n * @returns 返回修改的条目数量\n */\nconst performBulkToggle = async (enabled) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const selectedItems = Array.from(state.selectedItems);\n    if (selectedItems.length === 0) {\n        throw new Error('没有选中任何条目');\n    }\n    let modifiedCount = 0;\n    const booksToUpdate = new Map();\n    // 解析选中的条目并准备更新\n    for (const itemId of selectedItems) {\n        const [bookName, uid] = itemId.split('/');\n        if (bookName && uid) {\n            const entries = state.lorebookEntries.get(bookName) || [];\n            const updatedEntries = entries.map(entry => {\n                if (entry.uid === uid && entry.enabled !== enabled) {\n                    modifiedCount++;\n                    return { ...entry, enabled };\n                }\n                return entry;\n            });\n            if (updatedEntries.some((entry, index) => entry !== entries[index])) {\n                booksToUpdate.set(bookName, updatedEntries);\n            }\n        }\n    }\n    // 并行保存所有修改过的世界书\n    const updatePromises = Array.from(booksToUpdate.entries()).map(async ([bookName, entries]) => {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, entries);\n        if (result !== null) {\n            return () => state.lorebookEntries.set(bookName, entries);\n        }\n        return null;\n    });\n    const stateUpdaters = (await Promise.all(updatePromises)).filter(Boolean);\n    stateUpdaters.forEach(update => update());\n    // 清空选中状态并退出多选模式\n    state.selectedItems.clear();\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n        ...s,\n        lorebookEntries: new Map(state.lorebookEntries),\n        selectedItems: new Set(),\n        multiSelectMode: false,\n    }));\n    return modifiedCount;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvY29yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG1DQUFtQztBQUVEO0FBQzBCO0FBRzVEOzs7R0FHRztBQUNJLE1BQU0sV0FBVyxHQUFHLEtBQUssSUFBSSxFQUFFO0lBQ3BDLE1BQU0sWUFBWSxHQUFHLGdEQUFRLEVBQUUsQ0FBQztJQUVoQyxTQUFTO0lBQ1QsSUFBSSxZQUFZLENBQUMsU0FBUyxFQUFFLENBQUM7UUFDM0IsT0FBTyxDQUFDLEdBQUcsQ0FBQywwREFBMEQsQ0FBQyxDQUFDO1FBQ3hFLE9BQU87SUFDVCxDQUFDO0lBRUQsT0FBTztJQUNQLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2hCLEdBQUcsQ0FBQztRQUNKLFlBQVksRUFBRSxLQUFLO1FBQ25CLFNBQVMsRUFBRSxJQUFJO1FBQ2YsU0FBUyxFQUFFLElBQUk7S0FDaEIsQ0FBQyxDQUFDLENBQUM7SUFFSixJQUFJLENBQUM7UUFDSCxNQUFNLE9BQU8sR0FBRywyQ0FBUyxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBQ3ZDLE1BQU0sRUFBRSxVQUFVLEVBQUUsYUFBYSxFQUFFLFdBQVcsRUFBRSxNQUFNLEVBQUUsR0FBRyxPQUFPLENBQUM7UUFDbkUsTUFBTSxrQkFBa0IsR0FBRyxXQUFXLEtBQUssU0FBUyxJQUFJLFdBQVcsS0FBSyxJQUFJLENBQUM7UUFDN0UsTUFBTSxhQUFhLEdBQUcsTUFBTSxLQUFLLFNBQVMsSUFBSSxNQUFNLEtBQUssSUFBSSxDQUFDO1FBRTlELG1CQUFtQjtRQUNuQixNQUFNLE9BQU8sR0FBRyxNQUFNLE9BQU8sQ0FBQyxVQUFVLENBQUM7WUFDdkMsMkNBQVMsQ0FBQyxVQUFVLEVBQUU7WUFDdEIsMkNBQVMsQ0FBQyxtQkFBbUIsRUFBRTtZQUMvQiwyQ0FBUyxDQUFDLFlBQVksRUFBRTtZQUN4QixrQkFBa0IsQ0FBQyxDQUFDLENBQUMsMkNBQVMsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFDcEUsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLDJDQUFTLENBQUMsdUJBQXVCLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFDaEYsYUFBYSxDQUFDLENBQUMsQ0FBQywyQ0FBUyxDQUFDLGVBQWUsRUFBRSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQztTQUNwRSxDQUFDLENBQUM7UUFFSCw2QkFBNkI7UUFDN0IsTUFBTSxZQUFZLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUMvRSxNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ2pGLE1BQU0sZ0JBQWdCLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUNuRixNQUFNLFFBQVEsR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1FBQzdFLE1BQU0sZUFBZSxHQUFHLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7UUFDcEYsTUFBTSxZQUFZLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQztRQUVqRixlQUFlO1FBRWYsYUFBYTtRQUNiLE1BQU0sYUFBYSxHQUFHLENBQUMsWUFBWSxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLEtBQUssUUFBUSxDQUFDLENBQUM7UUFDN0UsTUFBTSxnQkFBZ0IsR0FBRyx1QkFBdUIsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7UUFFekUsZ0JBQWdCO1FBQ2hCLE1BQU0sa0JBQWtCLEdBQUcsSUFBSSxHQUFHLENBQUMsY0FBYyxFQUFFLHlCQUF5QixJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQ3BGLE1BQU0sWUFBWSxHQUFHLENBQUMsZ0JBQWdCLElBQUksRUFBRSxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN6RCxJQUFJLEVBQUUsSUFBSTtZQUNWLE9BQU8sRUFBRSxrQkFBa0IsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDO1NBQ3RDLENBQUMsQ0FBQyxDQUFDO1FBRUosZ0JBQWdCO1FBQ2hCLE1BQU0sV0FBVyxHQUFHLElBQUksR0FBRyxFQUFVLENBQUM7UUFDdEMsSUFBSSxlQUFlLEVBQUUsT0FBTztZQUFFLFdBQVcsQ0FBQyxHQUFHLENBQUMsZUFBZSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3ZFLElBQUksZUFBZSxFQUFFLFVBQVU7WUFBRSxlQUFlLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUM3RixNQUFNLGtCQUFrQixHQUFHLEtBQUssQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7UUFFbkQscUNBQXFDO1FBQ3JDLE1BQU0sYUFBYSxHQUFHLElBQUksR0FBRyxFQUFvQixDQUFDO1FBQ2xELE1BQU0sY0FBYyxHQUFHLElBQUksR0FBRyxDQUFTLGdCQUFnQixJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBRS9ELElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsRUFBRSxDQUFDO1lBQ2pDLEtBQUssTUFBTSxJQUFJLElBQUksYUFBYSxFQUFFLENBQUM7Z0JBQ2pDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSTtvQkFBRSxTQUFTO2dCQUMxQixpREFBaUQ7Z0JBQ2pELDJCQUEyQjtnQkFDM0IsTUFBTSxLQUFLLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGdCQUFnQixDQUFDLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO2dCQUNwRSxNQUFNLFNBQVMsR0FBRyxJQUFJLEdBQUcsRUFBVSxDQUFDO2dCQUNwQyxJQUFJLEtBQUssRUFBRSxPQUFPO29CQUFFLFNBQVMsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUNqRCxJQUFJLEtBQUssRUFBRSxVQUFVO29CQUFFLEtBQUssQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUV2RSxTQUFTLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxFQUFFO29CQUMzQixJQUFJLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDO3dCQUNqQyxhQUFhLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsQ0FBQztvQkFDbEMsQ0FBQztvQkFDRCxhQUFhLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7b0JBQzdDLGNBQWMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxnQkFBZ0I7Z0JBQ2hELENBQUMsQ0FBQyxDQUFDO1lBQ0wsQ0FBQztRQUNILENBQUM7UUFFRCxvQkFBb0I7UUFDcEIsa0JBQWtCLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsY0FBYyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3ZELElBQUksWUFBWSxFQUFFLENBQUM7WUFDakIsY0FBYyxDQUFDLEdBQUcsQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUNuQyxDQUFDO1FBRUQsa0JBQWtCO1FBQ2xCLE1BQU0sZUFBZSxHQUFHLElBQUksR0FBRyxFQUEyQixDQUFDO1FBQzNELE1BQU0sYUFBYSxHQUFHLEtBQUssQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBQyxJQUFJLEVBQUMsRUFBRTtZQUNoRSxNQUFNLE9BQU8sR0FBRyxNQUFNLDJDQUFTLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekQsSUFBSSxPQUFPLEVBQUUsQ0FBQztnQkFDWixlQUFlLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztZQUNyQyxDQUFDO1FBQ0gsQ0FBQyxDQUFDLENBQUM7UUFDSCxNQUFNLE9BQU8sQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLENBQUM7UUFFakMsWUFBWTtRQUNaLGtEQUFVLENBQUM7WUFDVCxhQUFhO1lBQ2IsZ0JBQWdCO1lBQ2hCLFlBQVk7WUFDWixrQkFBa0I7WUFDbEIsWUFBWSxFQUFFLFlBQVksSUFBSSxJQUFJO1lBQ2xDLGVBQWU7WUFDZixhQUFhO1NBQ2QsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHFDQUFxQyxFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQzVELE1BQU0sWUFBWSxHQUFHLEtBQUssWUFBWSxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQztRQUNyRSxtREFBVyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUNoQixHQUFHLENBQUM7WUFDSixZQUFZLEVBQUUsS0FBSyxFQUFFLFVBQVU7WUFDL0IsU0FBUyxFQUFFLEtBQUs7WUFDaEIsU0FBUyxFQUFFLFdBQVcsWUFBWSxFQUFFO1NBQ3JDLENBQUMsQ0FBQyxDQUFDO0lBQ04sQ0FBQztZQUFTLENBQUM7UUFDVCxjQUFjO1FBQ2QsbURBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ2pELENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7Ozs7R0FLRztBQUNILFNBQVMsdUJBQXVCLENBQUMsWUFBa0MsRUFBRSxRQUFhO0lBQ2hGLE1BQU0sa0JBQWtCLEdBQUcsWUFBWSxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLEtBQUssV0FBVyxDQUFDLElBQUksRUFBRSxDQUFDO0lBQ3BGLElBQUksV0FBVyxHQUFrQixFQUFFLENBQUM7SUFFcEMsSUFBSSxRQUFRLElBQUksMkNBQVMsQ0FBQyxTQUFTLEVBQUUsQ0FBQztRQUNwQyxJQUFJLENBQUM7WUFDSCxNQUFNLFNBQVMsR0FBRyxJQUFJLDJDQUFTLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3BELFdBQVcsR0FBRyxDQUFDLFNBQVMsQ0FBQyxlQUFlLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO2dCQUMvRCxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsSUFBSSxRQUFRLElBQUksQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLEVBQUU7Z0JBQ3JDLFdBQVcsRUFBRSxDQUFDLENBQUMsVUFBVSxJQUFJLFNBQVM7Z0JBQ3RDLFVBQVUsRUFBRSxDQUFDLENBQUMsU0FBUztnQkFDdkIsY0FBYyxFQUFFLENBQUMsQ0FBQyxhQUFhO2dCQUMvQixPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUMsUUFBUTtnQkFDcEIsS0FBSyxFQUFFLFdBQVc7Z0JBQ2xCLE1BQU0sRUFBRSxNQUFNO2FBQ2YsQ0FBQyxDQUFDLENBQUM7UUFDTixDQUFDO1FBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNYLE9BQU8sQ0FBQyxJQUFJLENBQUMseURBQXlELEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDN0UsQ0FBQztJQUNILENBQUM7SUFFRCxnQkFBZ0I7SUFDaEIsTUFBTSxrQkFBa0IsR0FBRyxJQUFJLEdBQUcsQ0FDaEMsa0JBQWtCLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsV0FBVyxLQUFLLENBQUMsQ0FBQyxVQUFVLEtBQUssQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQ3RGLENBQUM7SUFDRixNQUFNLGlCQUFpQixHQUFHLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUU7UUFDL0MsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLENBQUMsV0FBVyxLQUFLLENBQUMsQ0FBQyxVQUFVLEtBQUssQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDO1FBQzVFLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7SUFDN0MsQ0FBQyxDQUFDLENBQUM7SUFFSCxPQUFPLENBQUMsR0FBRyxrQkFBa0IsRUFBRSxHQUFHLGlCQUFpQixDQUFDLENBQUM7QUFDdkQsQ0FBQztBQUVELG1DQUFtQztBQUVuQzs7O0dBR0c7QUFDSSxNQUFNLGNBQWMsR0FBRyxLQUFLLEVBQUUsUUFBZ0IsRUFBRSxFQUFFO0lBQ3ZELE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxjQUFjLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDeEQsSUFBSSxNQUFNLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDcEIsTUFBTSxXQUFXLEVBQUUsQ0FBQyxDQUFDLGtCQUFrQjtJQUN6QyxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUY7Ozs7R0FJRztBQUNJLE1BQU0sY0FBYyxHQUFHLEtBQUssRUFBRSxPQUFlLEVBQUUsT0FBZSxFQUFFLEVBQUU7SUFDdkUsZUFBZTtJQUNmLE1BQU0sT0FBTyxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUM1RCxJQUFJLE9BQU8sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNyQixNQUFNLElBQUksS0FBSyxDQUFDLHVDQUF1QyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3BFLENBQUM7SUFFRCxZQUFZO0lBQ1osTUFBTSxZQUFZLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUM3RCxJQUFJLFlBQVksS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUMxQixNQUFNLElBQUksS0FBSyxDQUFDLGtDQUFrQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQy9ELENBQUM7SUFFRCxjQUFjO0lBQ2QsSUFBSSxPQUFPLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3ZCLE1BQU0sU0FBUyxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFDdkUsSUFBSSxTQUFTLEtBQUssSUFBSSxFQUFFLENBQUM7WUFDdkIscUJBQXFCO1lBQ3JCLE1BQU0sMkNBQVMsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDeEMsTUFBTSxJQUFJLEtBQUssQ0FBQywyQ0FBMkMsT0FBTyxFQUFFLENBQUMsQ0FBQztRQUN4RSxDQUFDO0lBQ0gsQ0FBQztJQUVELFVBQVU7SUFDVixNQUFNLFlBQVksR0FBRyxNQUFNLDJDQUFTLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQzdELElBQUksWUFBWSxLQUFLLElBQUksRUFBRSxDQUFDO1FBQzFCLG9CQUFvQjtRQUNwQixPQUFPLENBQUMsSUFBSSxDQUFDLGtDQUFrQyxPQUFPLG1CQUFtQixDQUFDLENBQUM7SUFDN0UsQ0FBQztJQUVELFVBQVU7SUFDVixNQUFNLFdBQVcsRUFBRSxDQUFDO0FBQ3RCLENBQUMsQ0FBQztBQUVGOzs7R0FHRztBQUNJLE1BQU0sY0FBYyxHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFFLEVBQUU7SUFDdkQsTUFBTSxNQUFNLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUN4RCxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNwQixNQUFNLFdBQVcsRUFBRSxDQUFDO0lBQ3RCLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRix5Q0FBeUM7QUFFekM7Ozs7R0FJRztBQUNJLE1BQU0sbUJBQW1CLEdBQUcsS0FBSyxFQUFFLFFBQWdCLEVBQUUsU0FBd0IsRUFBRSxFQUFFO0lBQ3RGLG1CQUFtQjtJQUNuQixNQUFNLE1BQU0sR0FBRyxNQUFNLDJDQUFTLENBQUMscUJBQXFCLENBQUMsUUFBUSxFQUFFLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQztJQUM1RSxJQUFJLE1BQU0sS0FBSyxJQUFJLElBQUksTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUN6QyxNQUFNLFFBQVEsR0FBRyxnREFBUSxFQUFFLENBQUM7UUFDNUIsTUFBTSxRQUFRLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMscUJBQXFCO1FBQ2pELE1BQU0sY0FBYyxHQUFHLFFBQVEsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUNwRSxRQUFRLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxHQUFHLGNBQWMsRUFBRSxRQUFRLENBQUMsQ0FBQyxDQUFDO1FBQ3RFLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUUsZUFBZSxFQUFFLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUNuRixDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUY7Ozs7O0dBS0c7QUFDSSxNQUFNLG1CQUFtQixHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFFLEdBQVcsRUFBRSxPQUErQixFQUFFLEVBQUU7SUFDMUcsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBQ3pCLE1BQU0sT0FBTyxHQUFHLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3BELElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUNiLE1BQU0sSUFBSSxLQUFLLENBQUMsdUNBQXVDLFFBQVEsRUFBRSxDQUFDLENBQUM7SUFDckUsQ0FBQztJQUVELElBQUksWUFBWSxHQUFHLEtBQUssQ0FBQztJQUN6QixNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFO1FBQ3pDLElBQUksS0FBSyxDQUFDLEdBQUcsS0FBSyxHQUFHLEVBQUUsQ0FBQztZQUN0QixZQUFZLEdBQUcsSUFBSSxDQUFDO1lBQ3BCLE9BQU8sRUFBRSxHQUFHLEtBQUssRUFBRSxHQUFHLE9BQU8sRUFBRSxDQUFDO1FBQ2xDLENBQUM7UUFDRCxPQUFPLEtBQUssQ0FBQztJQUNmLENBQUMsQ0FBQyxDQUFDO0lBRUgsSUFBSSxZQUFZLEVBQUUsQ0FBQztRQUNqQixNQUFNLE1BQU0sR0FBRyxNQUFNLDJDQUFTLENBQUMsa0JBQWtCLENBQUMsUUFBUSxFQUFFLGNBQWMsQ0FBQyxDQUFDO1FBQzVFLElBQUksTUFBTSxLQUFLLElBQUksRUFBRSxDQUFDO1lBQ3BCLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxjQUFjLENBQUMsQ0FBQztZQUNwRCxtREFBVyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLGVBQWUsRUFBRSxJQUFJLEdBQUcsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDaEYsQ0FBQztJQUNILENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7OztHQUlHO0FBQ0ksTUFBTSxtQkFBbUIsR0FBRyxLQUFLLEVBQUUsUUFBZ0IsRUFBRSxHQUFXLEVBQUUsRUFBRTtJQUN6RSxNQUFNLE1BQU0sR0FBRyxNQUFNLDJDQUFTLENBQUMscUJBQXFCLENBQUMsUUFBUSxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQztJQUN0RSxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNwQixNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7UUFDekIsTUFBTSxPQUFPLEdBQUcsS0FBSyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxDQUFDO1FBQzFELE1BQU0sZUFBZSxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsR0FBRyxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ25FLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxlQUFlLENBQUMsQ0FBQztRQUNyRCxtREFBVyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLGVBQWUsRUFBRSxJQUFJLEdBQUcsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDaEYsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGLDBCQUEwQjtBQUUxQjs7OztHQUlHO0FBQ0ksTUFBTSx3QkFBd0IsR0FBRyxLQUFLLEVBQUUsUUFBZ0IsRUFBRSxPQUFnQixFQUFFLEVBQUU7SUFDbkYsTUFBTSxRQUFRLEdBQUcsQ0FBQyxNQUFNLDJDQUFTLENBQUMsbUJBQW1CLEVBQUUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUMvRCxJQUFJLENBQUMsUUFBUSxDQUFDLHlCQUF5QixFQUFFLENBQUM7UUFDeEMsUUFBUSxDQUFDLHlCQUF5QixHQUFHLEVBQUUsQ0FBQztJQUMxQyxDQUFDO0lBRUQsTUFBTSxZQUFZLEdBQUcsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLHlCQUF5QixDQUFDLENBQUM7SUFDakUsSUFBSSxPQUFPLEVBQUUsQ0FBQztRQUNaLFlBQVksQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDN0IsQ0FBQztTQUFNLENBQUM7UUFDTixZQUFZLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2hDLENBQUM7SUFDRCxRQUFRLENBQUMseUJBQXlCLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztJQUU5RCxNQUFNLE1BQU0sR0FBRyxNQUFNLDJDQUFTLENBQUMsbUJBQW1CLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDN0QsSUFBSSxNQUFNLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDcEIsa0JBQWtCO1FBQ2xCLE1BQU0sS0FBSyxHQUFHLGdEQUFRLEVBQUUsQ0FBQztRQUN6QixNQUFNLElBQUksR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLEtBQUssUUFBUSxDQUFDLENBQUM7UUFDL0QsSUFBSSxJQUFJLEVBQUUsQ0FBQztZQUNULElBQUksQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDO1lBQ3ZCLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUUsWUFBWSxFQUFFLENBQUMsR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDdEUsQ0FBQztJQUNILENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRixnQ0FBZ0M7QUFFaEM7OztHQUdHO0FBQ0gsTUFBTSxrQkFBa0IsR0FBRyxLQUFLLEVBQUUsV0FBMEIsRUFBRSxFQUFFO0lBQzlELE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLENBQUM7SUFDM0QsSUFBSSxNQUFNLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDcEIseUJBQXlCO1FBQ3pCLHlCQUF5QjtRQUN6QixNQUFNLFdBQVcsRUFBRSxDQUFDO0lBQ3RCLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7O0dBR0c7QUFDSSxNQUFNLFdBQVcsR0FBRyxLQUFLLEVBQUUsWUFBa0MsRUFBRSxFQUFFO0lBQ3RFLE1BQU0sS0FBSyxHQUFHLGdEQUFRLEVBQUUsQ0FBQztJQUN6QixhQUFhO0lBQ2IsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDO1FBQ3hELEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxNQUFNLENBQUM7S0FDNUQsQ0FBQztJQUVGLE1BQU0sYUFBYSxHQUFnQjtRQUNqQyxFQUFFLEVBQUUsTUFBTSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7UUFDdEIsT0FBTyxFQUFFLElBQUk7UUFDYixNQUFNLEVBQUUsSUFBSTtRQUNaLEdBQUcsWUFBWTtLQUNELENBQUM7SUFFakIsTUFBTSxrQkFBa0IsQ0FBQyxDQUFDLEdBQUcsZ0JBQWdCLEVBQUUsYUFBYSxDQUFDLENBQUMsQ0FBQztBQUNqRSxDQUFDLENBQUM7QUFFRjs7OztHQUlHO0FBQ0ksTUFBTSxXQUFXLEdBQUcsS0FBSyxFQUFFLE9BQWUsRUFBRSxPQUE2QixFQUFFLEVBQUU7SUFDbEYsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBQ3pCLE1BQU0sZ0JBQWdCLEdBQUc7UUFDdkIsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLE1BQU0sQ0FBQztRQUN4RCxHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDO0tBQzVELENBQUM7SUFFRixNQUFNLFdBQVcsR0FBRyxnQkFBZ0IsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEtBQUssT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLEdBQUcsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFN0YsTUFBTSxrQkFBa0IsQ0FBQyxXQUFXLENBQUMsQ0FBQztBQUN4QyxDQUFDLENBQUM7QUFFRjs7O0dBR0c7QUFDSSxNQUFNLFdBQVcsR0FBRyxLQUFLLEVBQUUsT0FBZSxFQUFFLEVBQUU7SUFDbkQsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBQ3pCLE1BQU0sZ0JBQWdCLEdBQUc7UUFDdkIsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLE1BQU0sQ0FBQztRQUN4RCxHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDO0tBQzVELENBQUM7SUFFRixNQUFNLFdBQVcsR0FBRyxnQkFBZ0IsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxLQUFLLE9BQU8sQ0FBQyxDQUFDO0lBRW5FLE1BQU0sa0JBQWtCLENBQUMsV0FBVyxDQUFDLENBQUM7QUFDeEMsQ0FBQyxDQUFDO0FBRUYsMEJBQTBCO0FBRTFCOzs7OztHQUtHO0FBQ0ksTUFBTSxrQkFBa0IsR0FBRyxLQUFLLEVBQUUsVUFBa0IsRUFBRSxXQUFtQixFQUFtQixFQUFFO0lBQ25HLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQztRQUN2QixNQUFNLElBQUksS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBQzdCLENBQUM7SUFFRCxNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7SUFDekIsSUFBSSxhQUFhLEdBQUcsQ0FBQyxDQUFDO0lBQ3RCLE1BQU0sYUFBYSxHQUFHLElBQUksR0FBRyxFQUEyQixDQUFDO0lBRXpELGlCQUFpQjtJQUNqQixNQUFNLFdBQVcsR0FBRyxJQUFJLE1BQU0sQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLHFCQUFxQixFQUFFLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO0lBRXhGLGFBQWE7SUFDYixLQUFLLE1BQU0sQ0FBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLElBQUksS0FBSyxDQUFDLGVBQWUsQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDO1FBQ2xFLE1BQU0sZUFBZSxHQUFvQixFQUFFLENBQUM7UUFDNUMsSUFBSSxjQUFjLEdBQUcsS0FBSyxDQUFDO1FBRTNCLEtBQUssTUFBTSxLQUFLLElBQUksT0FBTyxFQUFFLENBQUM7WUFDNUIsSUFBSSxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBQzFCLE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxLQUFLLEVBQUUsQ0FBQztZQUVsQyxtQkFBbUI7WUFDbkIsSUFBSSxZQUFZLENBQUMsT0FBTyxFQUFFLENBQUM7Z0JBQ3pCLE1BQU0sVUFBVSxHQUFHLFlBQVksQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLFdBQVcsRUFBRSxXQUFXLENBQUMsQ0FBQztnQkFDMUUsSUFBSSxVQUFVLEtBQUssWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFDO29CQUN4QyxZQUFZLENBQUMsT0FBTyxHQUFHLFVBQVUsQ0FBQztvQkFDbEMsYUFBYSxHQUFHLElBQUksQ0FBQztnQkFDdkIsQ0FBQztZQUNILENBQUM7WUFFRCxRQUFRO1lBQ1IsSUFBSSxZQUFZLENBQUMsSUFBSSxJQUFJLFlBQVksQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUN0RCxNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDdkQsWUFBWSxDQUFDLElBQUksR0FBRyxZQUFZLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsV0FBVyxFQUFFLFdBQVcsQ0FBQyxDQUFDLENBQUM7Z0JBQ3hGLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssWUFBWSxFQUFFLENBQUM7b0JBQ3ZELGFBQWEsR0FBRyxJQUFJLENBQUM7Z0JBQ3ZCLENBQUM7WUFDSCxDQUFDO1lBRUQsT0FBTztZQUNQLElBQUksWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUN6QixNQUFNLFVBQVUsR0FBRyxZQUFZLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxXQUFXLEVBQUUsV0FBVyxDQUFDLENBQUM7Z0JBQzFFLElBQUksVUFBVSxLQUFLLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQztvQkFDeEMsWUFBWSxDQUFDLE9BQU8sR0FBRyxVQUFVLENBQUM7b0JBQ2xDLGFBQWEsR0FBRyxJQUFJLENBQUM7Z0JBQ3ZCLENBQUM7WUFDSCxDQUFDO1lBRUQsSUFBSSxhQUFhLEVBQUUsQ0FBQztnQkFDbEIsYUFBYSxFQUFFLENBQUM7Z0JBQ2hCLGNBQWMsR0FBRyxJQUFJLENBQUM7WUFDeEIsQ0FBQztZQUVELGVBQWUsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDckMsQ0FBQztRQUVELElBQUksY0FBYyxFQUFFLENBQUM7WUFDbkIsYUFBYSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsZUFBZSxDQUFDLENBQUM7UUFDL0MsQ0FBQztJQUNILENBQUM7SUFFRCxnQkFBZ0I7SUFDaEIsTUFBTSxjQUFjLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFFLENBQUMsUUFBUSxFQUFFLE9BQU8sQ0FBQyxFQUFFLEVBQUU7UUFDM0YsTUFBTSxNQUFNLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGtCQUFrQixDQUFDLFFBQVEsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUNyRSxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztZQUNwQiw0QkFBNEI7WUFDNUIsT0FBTyxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFDNUQsQ0FBQztRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQyxDQUFDLENBQUM7SUFFSCxNQUFNLGFBQWEsR0FBRyxDQUFDLE1BQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUMxRSxhQUFhLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsTUFBTyxFQUFFLENBQUMsQ0FBQztJQUUzQyxJQUFJLGFBQWEsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDM0IsbURBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxlQUFlLEVBQUUsSUFBSSxHQUFHLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ2hGLENBQUM7SUFFRCxPQUFPLGFBQWEsQ0FBQztBQUN2QixDQUFDLENBQUM7QUFFRjs7O0dBR0c7QUFDSSxNQUFNLGlCQUFpQixHQUFHLEtBQUssSUFBcUIsRUFBRTtJQUMzRCxNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7SUFDekIsTUFBTSxhQUFhLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUM7SUFFdEQsSUFBSSxhQUFhLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1FBQy9CLE1BQU0sSUFBSSxLQUFLLENBQUMsVUFBVSxDQUFDLENBQUM7SUFDOUIsQ0FBQztJQUVELElBQUksWUFBWSxHQUFHLENBQUMsQ0FBQztJQUNyQixNQUFNLGFBQWEsR0FBRyxJQUFJLEdBQUcsRUFBb0IsQ0FBQyxDQUFDLDZCQUE2QjtJQUVoRixVQUFVO0lBQ1YsS0FBSyxNQUFNLE1BQU0sSUFBSSxhQUFhLEVBQUUsQ0FBQztRQUNuQyxNQUFNLENBQUMsUUFBUSxFQUFFLEdBQUcsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDMUMsSUFBSSxRQUFRLElBQUksR0FBRyxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztnQkFDakMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDbEMsQ0FBQztZQUNELGFBQWEsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3pDLENBQUM7SUFDSCxDQUFDO0lBRUQsZ0JBQWdCO0lBQ2hCLE1BQU0sY0FBYyxHQUFHLEtBQUssQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxDQUFDLFFBQVEsRUFBRSxZQUFZLENBQUMsRUFBRSxFQUFFO1FBQ2hHLE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLEVBQUUsWUFBWSxDQUFDLENBQUM7UUFDN0UsSUFBSSxNQUFNLEtBQUssSUFBSSxFQUFFLENBQUM7WUFDcEIsT0FBTyxFQUFFLFFBQVEsRUFBRSxZQUFZLEVBQUUsS0FBSyxFQUFFLFlBQVksQ0FBQyxNQUFNLEVBQUUsQ0FBQztRQUNoRSxDQUFDO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDLENBQUMsQ0FBQztJQUVILE1BQU0sYUFBYSxHQUFHLENBQUMsTUFBTSxPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBRTFFLEtBQUssTUFBTSxNQUFNLElBQUksYUFBYSxFQUFFLENBQUM7UUFDbkMsSUFBSSxDQUFDLE1BQU07WUFBRSxTQUFTO1FBQ3RCLFlBQVksSUFBSSxNQUFNLENBQUMsS0FBSyxDQUFDO1FBQzdCLFNBQVM7UUFDVCxNQUFNLE9BQU8sR0FBRyxLQUFLLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ2pFLE1BQU0sZUFBZSxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQzFGLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsZUFBZSxDQUFDLENBQUM7SUFDOUQsQ0FBQztJQUVELFNBQVM7SUFDVCxLQUFLLENBQUMsYUFBYSxDQUFDLEtBQUssRUFBRSxDQUFDO0lBQzVCLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2hCLEdBQUcsQ0FBQztRQUNKLGVBQWUsRUFBRSxJQUFJLEdBQUcsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDO1FBQy9DLGFBQWEsRUFBRSxJQUFJLEdBQUcsRUFBRTtRQUN4QixlQUFlLEVBQUUsS0FBSztLQUN2QixDQUFDLENBQUMsQ0FBQztJQUVKLE9BQU8sWUFBWSxDQUFDO0FBQ3RCLENBQUMsQ0FBQztBQUVGOzs7R0FHRztBQUNJLE1BQU0saUJBQWlCLEdBQUcsS0FBSyxJQUFxQixFQUFFO0lBQzNELE9BQU8sTUFBTSxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUN2QyxDQUFDLENBQUM7QUFFRjs7O0dBR0c7QUFDSSxNQUFNLGtCQUFrQixHQUFHLEtBQUssSUFBcUIsRUFBRTtJQUM1RCxPQUFPLE1BQU0saUJBQWlCLENBQUMsS0FBSyxDQUFDLENBQUM7QUFDeEMsQ0FBQyxDQUFDO0FBRUY7Ozs7R0FJRztBQUNILE1BQU0saUJBQWlCLEdBQUcsS0FBSyxFQUFFLE9BQWdCLEVBQW1CLEVBQUU7SUFDcEUsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBQ3pCLE1BQU0sYUFBYSxHQUFHLEtBQUssQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxDQUFDO0lBRXRELElBQUksYUFBYSxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUMvQixNQUFNLElBQUksS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQzlCLENBQUM7SUFFRCxJQUFJLGFBQWEsR0FBRyxDQUFDLENBQUM7SUFDdEIsTUFBTSxhQUFhLEdBQUcsSUFBSSxHQUFHLEVBQTJCLENBQUM7SUFFekQsZUFBZTtJQUNmLEtBQUssTUFBTSxNQUFNLElBQUksYUFBYSxFQUFFLENBQUM7UUFDbkMsTUFBTSxDQUFDLFFBQVEsRUFBRSxHQUFHLENBQUMsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQzFDLElBQUksUUFBUSxJQUFJLEdBQUcsRUFBRSxDQUFDO1lBQ3BCLE1BQU0sT0FBTyxHQUFHLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUMxRCxNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFO2dCQUN6QyxJQUFJLEtBQUssQ0FBQyxHQUFHLEtBQUssR0FBRyxJQUFJLEtBQUssQ0FBQyxPQUFPLEtBQUssT0FBTyxFQUFFLENBQUM7b0JBQ25ELGFBQWEsRUFBRSxDQUFDO29CQUNoQixPQUFPLEVBQUUsR0FBRyxLQUFLLEVBQUUsT0FBTyxFQUFFLENBQUM7Z0JBQy9CLENBQUM7Z0JBQ0QsT0FBTyxLQUFLLENBQUM7WUFDZixDQUFDLENBQUMsQ0FBQztZQUVILElBQUksY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDLEtBQUssRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEtBQUssS0FBSyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDO2dCQUNwRSxhQUFhLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxjQUFjLENBQUMsQ0FBQztZQUM5QyxDQUFDO1FBQ0gsQ0FBQztJQUNILENBQUM7SUFFRCxnQkFBZ0I7SUFDaEIsTUFBTSxjQUFjLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFFLENBQUMsUUFBUSxFQUFFLE9BQU8sQ0FBQyxFQUFFLEVBQUU7UUFDM0YsTUFBTSxNQUFNLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGtCQUFrQixDQUFDLFFBQVEsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUNyRSxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztZQUNwQixPQUFPLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUM1RCxDQUFDO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDLENBQUMsQ0FBQztJQUVILE1BQU0sYUFBYSxHQUFHLENBQUMsTUFBTSxPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQzFFLGFBQWEsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxNQUFPLEVBQUUsQ0FBQyxDQUFDO0lBRTNDLGdCQUFnQjtJQUNoQixLQUFLLENBQUMsYUFBYSxDQUFDLEtBQUssRUFBRSxDQUFDO0lBQzVCLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2hCLEdBQUcsQ0FBQztRQUNKLGVBQWUsRUFBRSxJQUFJLEdBQUcsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDO1FBQy9DLGFBQWEsRUFBRSxJQUFJLEdBQUcsRUFBRTtRQUN4QixlQUFlLEVBQUUsS0FBSztLQUN2QixDQUFDLENBQUMsQ0FBQztJQUVKLE9BQU8sYUFBYSxDQUFDO0FBQ3ZCLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvY29yZS50cz8iXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3dvcmxkX2luZm9fb3B0aW1pemVyL2NvcmUudHNcblxuaW1wb3J0IHsgVGF2ZXJuQVBJIH0gZnJvbSAnLi9hcGknO1xuaW1wb3J0IHsgZ2V0U3RhdGUsIHNldEFsbERhdGEsIHVwZGF0ZVN0YXRlIH0gZnJvbSAnLi9zdG9yZSc7XG5pbXBvcnQgeyBMb3JlYm9va0VudHJ5LCBUYXZlcm5SZWdleCB9IGZyb20gJy4vdHlwZXMnO1xuXG4vKipcbiAqIOWKoOi9veaJgOacieWIneWni+aVsOaNruOAglxuICog6L+Z5piv5bqU55So5ZCv5Yqo5pe26LCD55So55qE5Li76KaB5pWw5o2u6I635Y+W5Ye95pWw44CCXG4gKi9cbmV4cG9ydCBjb25zdCBsb2FkQWxsRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgY3VycmVudFN0YXRlID0gZ2V0U3RhdGUoKTtcblxuICAvLyDpmLLmraLlubblj5HliqDovb1cbiAgaWYgKGN1cnJlbnRTdGF0ZS5pc0xvYWRpbmcpIHtcbiAgICBjb25zb2xlLmxvZygnW1dJTyBDb3JlXSBEYXRhIGxvYWRpbmcgYWxyZWFkeSBpbiBwcm9ncmVzcywgc2tpcHBpbmcuLi4nKTtcbiAgICByZXR1cm47XG4gIH1cblxuICAvLyDlvIDlp4vliqDovb1cbiAgdXBkYXRlU3RhdGUocyA9PiAoe1xuICAgIC4uLnMsXG4gICAgaXNEYXRhTG9hZGVkOiBmYWxzZSxcbiAgICBpc0xvYWRpbmc6IHRydWUsXG4gICAgbG9hZEVycm9yOiBudWxsLFxuICB9KSk7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBjb250ZXh0ID0gVGF2ZXJuQVBJLmdldENvbnRleHQoKTtcbiAgICBjb25zdCB7IGNoYXJhY3RlcnM6IGFsbENoYXJhY3RlcnMsIGNoYXJhY3RlcklkLCBjaGF0SWQgfSA9IGNvbnRleHQ7XG4gICAgY29uc3QgaGFzQWN0aXZlQ2hhcmFjdGVyID0gY2hhcmFjdGVySWQgIT09IHVuZGVmaW5lZCAmJiBjaGFyYWN0ZXJJZCAhPT0gbnVsbDtcbiAgICBjb25zdCBoYXNBY3RpdmVDaGF0ID0gY2hhdElkICE9PSB1bmRlZmluZWQgJiYgY2hhdElkICE9PSBudWxsO1xuXG4gICAgLy8gLS0tIOW5tuihjOiOt+WPluWfuuehgOaVsOaNriAtLS1cbiAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGxTZXR0bGVkKFtcbiAgICAgIFRhdmVybkFQSS5nZXRSZWdleGVzKCksXG4gICAgICBUYXZlcm5BUEkuZ2V0TG9yZWJvb2tTZXR0aW5ncygpLFxuICAgICAgVGF2ZXJuQVBJLmdldExvcmVib29rcygpLFxuICAgICAgaGFzQWN0aXZlQ2hhcmFjdGVyID8gVGF2ZXJuQVBJLmdldENoYXJEYXRhKCkgOiBQcm9taXNlLnJlc29sdmUobnVsbCksXG4gICAgICBoYXNBY3RpdmVDaGFyYWN0ZXIgPyBUYXZlcm5BUEkuZ2V0Q3VycmVudENoYXJMb3JlYm9va3MoKSA6IFByb21pc2UucmVzb2x2ZShudWxsKSxcbiAgICAgIGhhc0FjdGl2ZUNoYXQgPyBUYXZlcm5BUEkuZ2V0Q2hhdExvcmVib29rKCkgOiBQcm9taXNlLnJlc29sdmUobnVsbCksXG4gICAgXSk7XG5cbiAgICAvLyAtLS0g5a6J5YWo5Zyw5LuOUHJvbWlzZee7k+aenOS4reaPkOWPluaVsOaNriAtLS1cbiAgICBjb25zdCBhbGxVSVJlZ2V4ZXMgPSByZXN1bHRzWzBdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzBdLnZhbHVlIDogW107XG4gICAgY29uc3QgZ2xvYmFsU2V0dGluZ3MgPSByZXN1bHRzWzFdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzFdLnZhbHVlIDoge307XG4gICAgY29uc3QgYWxsQm9va0ZpbGVOYW1lcyA9IHJlc3VsdHNbMl0uc3RhdHVzID09PSAnZnVsZmlsbGVkJyA/IHJlc3VsdHNbMl0udmFsdWUgOiBbXTtcbiAgICBjb25zdCBjaGFyRGF0YSA9IHJlc3VsdHNbM10uc3RhdHVzID09PSAnZnVsZmlsbGVkJyA/IHJlc3VsdHNbM10udmFsdWUgOiBudWxsO1xuICAgIGNvbnN0IGNoYXJMaW5rZWRCb29rcyA9IHJlc3VsdHNbNF0uc3RhdHVzID09PSAnZnVsZmlsbGVkJyA/IHJlc3VsdHNbNF0udmFsdWUgOiBudWxsO1xuICAgIGNvbnN0IGNoYXRMb3JlYm9vayA9IHJlc3VsdHNbNV0uc3RhdHVzID09PSAnZnVsZmlsbGVkJyA/IHJlc3VsdHNbNV0udmFsdWUgOiBudWxsO1xuXG4gICAgLy8gLS0tIOaVsOaNruWkhOeQhiAtLS1cblxuICAgIC8vIDEuIOato+WImeihqOi+vuW8j+WkhOeQhlxuICAgIGNvbnN0IGdsb2JhbFJlZ2V4ZXMgPSAoYWxsVUlSZWdleGVzIHx8IFtdKS5maWx0ZXIociA9PiByLnNjb3BlID09PSAnZ2xvYmFsJyk7XG4gICAgY29uc3QgY2hhcmFjdGVyUmVnZXhlcyA9IHByb2Nlc3NDaGFyYWN0ZXJSZWdleGVzKGFsbFVJUmVnZXhlcywgY2hhckRhdGEpO1xuXG4gICAgLy8gMi4g5LiW55WM5Lmm5paH5Lu25ZKM5ZCv55So54q25oCBXG4gICAgY29uc3QgZW5hYmxlZEdsb2JhbEJvb2tzID0gbmV3IFNldChnbG9iYWxTZXR0aW5ncz8uc2VsZWN0ZWRfZ2xvYmFsX2xvcmVib29rcyB8fCBbXSk7XG4gICAgY29uc3QgYWxsTG9yZWJvb2tzID0gKGFsbEJvb2tGaWxlTmFtZXMgfHwgW10pLm1hcChuYW1lID0+ICh7XG4gICAgICBuYW1lOiBuYW1lLFxuICAgICAgZW5hYmxlZDogZW5hYmxlZEdsb2JhbEJvb2tzLmhhcyhuYW1lKSxcbiAgICB9KSk7XG5cbiAgICAvLyAzLiDlvZPliY3op5LoibLlhbPogZTnmoTkuJbnlYzkuaZcbiAgICBjb25zdCBjaGFyQm9va1NldCA9IG5ldyBTZXQ8c3RyaW5nPigpO1xuICAgIGlmIChjaGFyTGlua2VkQm9va3M/LnByaW1hcnkpIGNoYXJCb29rU2V0LmFkZChjaGFyTGlua2VkQm9va3MucHJpbWFyeSk7XG4gICAgaWYgKGNoYXJMaW5rZWRCb29rcz8uYWRkaXRpb25hbCkgY2hhckxpbmtlZEJvb2tzLmFkZGl0aW9uYWwuZm9yRWFjaChiID0+IGNoYXJCb29rU2V0LmFkZChiKSk7XG4gICAgY29uc3QgY2hhcmFjdGVyTG9yZWJvb2tzID0gQXJyYXkuZnJvbShjaGFyQm9va1NldCk7XG5cbiAgICAvLyA0LiDorqHnrpfmiYDmnInop5LoibLlr7nkuJbnlYzkuabnmoTkvb/nlKjmg4XlhrUgKGxvcmVib29rVXNhZ2UpXG4gICAgY29uc3QgbG9yZWJvb2tVc2FnZSA9IG5ldyBNYXA8c3RyaW5nLCBzdHJpbmdbXT4oKTtcbiAgICBjb25zdCBrbm93bkJvb2tOYW1lcyA9IG5ldyBTZXQ8c3RyaW5nPihhbGxCb29rRmlsZU5hbWVzIHx8IFtdKTtcblxuICAgIGlmIChBcnJheS5pc0FycmF5KGFsbENoYXJhY3RlcnMpKSB7XG4gICAgICBmb3IgKGNvbnN0IGNoYXIgb2YgYWxsQ2hhcmFjdGVycykge1xuICAgICAgICBpZiAoIWNoYXI/Lm5hbWUpIGNvbnRpbnVlO1xuICAgICAgICAvLyDms6jmhI86IGdldENoYXJMb3JlYm9va3Mg5piv5ZCM5q2l6L+Y5piv5byC5q2l5Y+W5Yaz5LqOVGF2ZXJuSGVscGVy55qE5a6e546wXG4gICAgICAgIC8vIOS4uuS/nemZqei1t+inge+8jOaIkeS7rOWBh+iuvuWug+WPr+iDvei/lOWbnuS4gOS4qlByb21pc2VcbiAgICAgICAgY29uc3QgYm9va3MgPSBhd2FpdCBUYXZlcm5BUEkuZ2V0Q2hhckxvcmVib29rcyh7IG5hbWU6IGNoYXIubmFtZSB9KTtcbiAgICAgICAgY29uc3QgY2hhckJvb2tzID0gbmV3IFNldDxzdHJpbmc+KCk7XG4gICAgICAgIGlmIChib29rcz8ucHJpbWFyeSkgY2hhckJvb2tzLmFkZChib29rcy5wcmltYXJ5KTtcbiAgICAgICAgaWYgKGJvb2tzPy5hZGRpdGlvbmFsKSBib29rcy5hZGRpdGlvbmFsLmZvckVhY2goYiA9PiBjaGFyQm9va3MuYWRkKGIpKTtcblxuICAgICAgICBjaGFyQm9va3MuZm9yRWFjaChib29rTmFtZSA9PiB7XG4gICAgICAgICAgaWYgKCFsb3JlYm9va1VzYWdlLmhhcyhib29rTmFtZSkpIHtcbiAgICAgICAgICAgIGxvcmVib29rVXNhZ2Uuc2V0KGJvb2tOYW1lLCBbXSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGxvcmVib29rVXNhZ2UuZ2V0KGJvb2tOYW1lKT8ucHVzaChjaGFyLm5hbWUpO1xuICAgICAgICAgIGtub3duQm9va05hbWVzLmFkZChib29rTmFtZSk7IC8vIOehruS/neaJgOacieiiq+S9v+eUqOeahOS5pumDveiiq+WKoOi9vVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyA1LiDmsYfmgLvmiYDmnInpnIDopoHliqDovb3mnaHnm67nmoTkuJbnlYzkuaZcbiAgICBjaGFyYWN0ZXJMb3JlYm9va3MuZm9yRWFjaChiID0+IGtub3duQm9va05hbWVzLmFkZChiKSk7XG4gICAgaWYgKGNoYXRMb3JlYm9vaykge1xuICAgICAga25vd25Cb29rTmFtZXMuYWRkKGNoYXRMb3JlYm9vayk7XG4gICAgfVxuXG4gICAgLy8gNi4g5bm26KGM5Yqg6L295omA5pyJ5LiW55WM5Lmm55qE5p2h55uuXG4gICAgY29uc3QgbG9yZWJvb2tFbnRyaWVzID0gbmV3IE1hcDxzdHJpbmcsIExvcmVib29rRW50cnlbXT4oKTtcbiAgICBjb25zdCBlbnRyeVByb21pc2VzID0gQXJyYXkuZnJvbShrbm93bkJvb2tOYW1lcykubWFwKGFzeW5jIG5hbWUgPT4ge1xuICAgICAgY29uc3QgZW50cmllcyA9IGF3YWl0IFRhdmVybkFQSS5nZXRMb3JlYm9va0VudHJpZXMobmFtZSk7XG4gICAgICBpZiAoZW50cmllcykge1xuICAgICAgICBsb3JlYm9va0VudHJpZXMuc2V0KG5hbWUsIGVudHJpZXMpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIGF3YWl0IFByb21pc2UuYWxsKGVudHJ5UHJvbWlzZXMpO1xuXG4gICAgLy8gNy4g5pu05paw5YWo5bGA54q25oCBXG4gICAgc2V0QWxsRGF0YSh7XG4gICAgICBnbG9iYWxSZWdleGVzLFxuICAgICAgY2hhcmFjdGVyUmVnZXhlcyxcbiAgICAgIGFsbExvcmVib29rcyxcbiAgICAgIGNoYXJhY3RlckxvcmVib29rcyxcbiAgICAgIGNoYXRMb3JlYm9vazogY2hhdExvcmVib29rIHx8IG51bGwsXG4gICAgICBsb3JlYm9va0VudHJpZXMsXG4gICAgICBsb3JlYm9va1VzYWdlLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1tXSU8gQ29yZV0gRmFpbGVkIHRvIGxvYWQgYWxsIGRhdGE6JywgZXJyb3IpO1xuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+acquefpemUmeivryc7XG4gICAgdXBkYXRlU3RhdGUocyA9PiAoe1xuICAgICAgLi4ucyxcbiAgICAgIGlzRGF0YUxvYWRlZDogZmFsc2UsIC8vIOehruS/nea4hemZpOaXp+aVsOaNrlxuICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgIGxvYWRFcnJvcjogYOaVsOaNruWKoOi9veWksei0pTogJHtlcnJvck1lc3NhZ2V9YCxcbiAgICB9KSk7XG4gIH0gZmluYWxseSB7XG4gICAgLy8g56Gu5L+d5Yqg6L2954q25oCB5oC75piv6KKr6YeN572uXG4gICAgdXBkYXRlU3RhdGUocyA9PiAoeyAuLi5zLCBpc0xvYWRpbmc6IGZhbHNlIH0pKTtcbiAgfVxufTtcblxuLyoqXG4gKiDlpITnkIblkozlkIjlubbmnaXoh6pVSeWSjOinkuiJsuWNoeS4reeahOinkuiJsueJueWumuato+WImeihqOi+vuW8j+OAglxuICogQHBhcmFtIGFsbFVJUmVnZXhlcyDku45UYXZlcm4gQVBJ6I635Y+W55qE5omA5pyJVUnmraPliJlcbiAqIEBwYXJhbSBjaGFyRGF0YSDlvZPliY3op5LoibLnmoTmlbDmja5cbiAqIEByZXR1cm5zIOWQiOW5tuWSjOWOu+mHjeWQjueahOinkuiJsuato+WImeihqOi+vuW8j+aVsOe7hFxuICovXG5mdW5jdGlvbiBwcm9jZXNzQ2hhcmFjdGVyUmVnZXhlcyhhbGxVSVJlZ2V4ZXM6IFRhdmVyblJlZ2V4W10gfCBudWxsLCBjaGFyRGF0YTogYW55KTogVGF2ZXJuUmVnZXhbXSB7XG4gIGNvbnN0IGNoYXJhY3RlclVJUmVnZXhlcyA9IGFsbFVJUmVnZXhlcz8uZmlsdGVyKHIgPT4gci5zY29wZSA9PT0gJ2NoYXJhY3RlcicpIHx8IFtdO1xuICBsZXQgY2FyZFJlZ2V4ZXM6IFRhdmVyblJlZ2V4W10gPSBbXTtcblxuICBpZiAoY2hhckRhdGEgJiYgVGF2ZXJuQVBJLkNoYXJhY3Rlcikge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBjaGFyYWN0ZXIgPSBuZXcgVGF2ZXJuQVBJLkNoYXJhY3RlcihjaGFyRGF0YSk7XG4gICAgICBjYXJkUmVnZXhlcyA9IChjaGFyYWN0ZXIuZ2V0UmVnZXhTY3JpcHRzKCkgfHwgW10pLm1hcCgociwgaSkgPT4gKHtcbiAgICAgICAgaWQ6IHIuaWQgfHwgYGNhcmQtJHtEYXRlLm5vdygpfS0ke2l9YCxcbiAgICAgICAgc2NyaXB0X25hbWU6IHIuc2NyaXB0TmFtZSB8fCAn5pyq5ZG95ZCN5Y2h5YaF5q2j5YiZJyxcbiAgICAgICAgZmluZF9yZWdleDogci5maW5kUmVnZXgsXG4gICAgICAgIHJlcGxhY2Vfc3RyaW5nOiByLnJlcGxhY2VTdHJpbmcsXG4gICAgICAgIGVuYWJsZWQ6ICFyLmRpc2FibGVkLFxuICAgICAgICBzY29wZTogJ2NoYXJhY3RlcicsXG4gICAgICAgIHNvdXJjZTogJ2NhcmQnLFxuICAgICAgfSkpO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGNvbnNvbGUud2FybihcIltXSU8gQ29yZV0gQ291bGRuJ3QgcGFyc2UgY2hhcmFjdGVyIGNhcmQgcmVnZXggc2NyaXB0czpcIiwgZSk7XG4gICAgfVxuICB9XG5cbiAgLy8g5Y676YeN6YC76L6R77yaVUnkuK3nmoTmraPliJnkvJjlhYhcbiAgY29uc3QgdWlSZWdleElkZW50aWZpZXJzID0gbmV3IFNldChcbiAgICBjaGFyYWN0ZXJVSVJlZ2V4ZXMubWFwKHIgPT4gYCR7ci5zY3JpcHRfbmFtZX06OiR7ci5maW5kX3JlZ2V4fTo6JHtyLnJlcGxhY2Vfc3RyaW5nfWApLFxuICApO1xuICBjb25zdCB1bmlxdWVDYXJkUmVnZXhlcyA9IGNhcmRSZWdleGVzLmZpbHRlcihyID0+IHtcbiAgICBjb25zdCBpZGVudGlmaWVyID0gYCR7ci5zY3JpcHRfbmFtZX06OiR7ci5maW5kX3JlZ2V4fTo6JHtyLnJlcGxhY2Vfc3RyaW5nfWA7XG4gICAgcmV0dXJuICF1aVJlZ2V4SWRlbnRpZmllcnMuaGFzKGlkZW50aWZpZXIpO1xuICB9KTtcblxuICByZXR1cm4gWy4uLmNoYXJhY3RlclVJUmVnZXhlcywgLi4udW5pcXVlQ2FyZFJlZ2V4ZXNdO1xufVxuXG4vLyAtLS0gTG9yZWJvb2sgQ1JVRCBPcGVyYXRpb25zIC0tLVxuXG4vKipcbiAqIOWIm+W7uuS4gOS4quaWsOeahOepuuS4lueVjOS5puOAglxuICogQHBhcmFtIGJvb2tOYW1lIOaWsOS4lueVjOS5pueahOWQjeensFxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlTG9yZWJvb2sgPSBhc3luYyAoYm9va05hbWU6IHN0cmluZykgPT4ge1xuICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuY3JlYXRlTG9yZWJvb2soYm9va05hbWUpO1xuICBpZiAocmVzdWx0ICE9PSBudWxsKSB7XG4gICAgYXdhaXQgbG9hZEFsbERhdGEoKTsgLy8g6YeN5paw5Yqg6L295omA5pyJ5pWw5o2u5Lul56Gu5L+d54q25oCB5ZCM5q2lXG4gIH1cbn07XG5cbi8qKlxuICog6YeN5ZG95ZCN5LiA5Liq5LiW55WM5Lmm44CCXG4gKiBAcGFyYW0gb2xkTmFtZSDml6flkI3np7BcbiAqIEBwYXJhbSBuZXdOYW1lIOaWsOWQjeensFxuICovXG5leHBvcnQgY29uc3QgcmVuYW1lTG9yZWJvb2sgPSBhc3luYyAob2xkTmFtZTogc3RyaW5nLCBuZXdOYW1lOiBzdHJpbmcpID0+IHtcbiAgLy8gMS4g6I635Y+W5pen5Lmm55qE5omA5pyJ5p2h55uuXG4gIGNvbnN0IGVudHJpZXMgPSBhd2FpdCBUYXZlcm5BUEkuZ2V0TG9yZWJvb2tFbnRyaWVzKG9sZE5hbWUpO1xuICBpZiAoZW50cmllcyA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBlbnRyaWVzIGZvciBsb3JlYm9vazogJHtvbGROYW1lfWApO1xuICB9XG5cbiAgLy8gMi4g5Yib5bu65LiA5Liq5paw5LmmXG4gIGNvbnN0IGNyZWF0ZVJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5jcmVhdGVMb3JlYm9vayhuZXdOYW1lKTtcbiAgaWYgKGNyZWF0ZVJlc3VsdCA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBuZXcgbG9yZWJvb2s6ICR7bmV3TmFtZX1gKTtcbiAgfVxuXG4gIC8vIDMuIOWwhuadoeebruWkjeWItuWIsOaWsOS5plxuICBpZiAoZW50cmllcy5sZW5ndGggPiAwKSB7XG4gICAgY29uc3Qgc2V0UmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLnNldExvcmVib29rRW50cmllcyhuZXdOYW1lLCBlbnRyaWVzKTtcbiAgICBpZiAoc2V0UmVzdWx0ID09PSBudWxsKSB7XG4gICAgICAvLyDmuIXnkIbvvJrlpoLmnpzmnaHnm67orr7nva7lpLHotKXvvIzliKDpmaTmlrDlu7rnmoTkuaZcbiAgICAgIGF3YWl0IFRhdmVybkFQSS5kZWxldGVMb3JlYm9vayhuZXdOYW1lKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHNldCBlbnRyaWVzIGZvciBuZXcgbG9yZWJvb2s6ICR7bmV3TmFtZX1gKTtcbiAgICB9XG4gIH1cblxuICAvLyA0LiDliKDpmaTml6fkuaZcbiAgY29uc3QgZGVsZXRlUmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLmRlbGV0ZUxvcmVib29rKG9sZE5hbWUpO1xuICBpZiAoZGVsZXRlUmVzdWx0ID09PSBudWxsKSB7XG4gICAgLy8g6L+Z5LiN5piv5LiA5Liq5YWz6ZSu6ZSZ6K+v77yM5L2G5bqU6K+l6K6w5b2V5LiL5p2lXG4gICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gZGVsZXRlIG9sZCBsb3JlYm9vayBcIiR7b2xkTmFtZX1cIiBhZnRlciByZW5hbWluZy5gKTtcbiAgfVxuXG4gIC8vIDUuIOWIt+aWsOaVsOaNrlxuICBhd2FpdCBsb2FkQWxsRGF0YSgpO1xufTtcblxuLyoqXG4gKiDliKDpmaTkuIDkuKrkuJbnlYzkuabjgIJcbiAqIEBwYXJhbSBib29rTmFtZSDopoHliKDpmaTnmoTkuJbnlYzkuabnmoTlkI3np7BcbiAqL1xuZXhwb3J0IGNvbnN0IGRlbGV0ZUxvcmVib29rID0gYXN5bmMgKGJvb2tOYW1lOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLmRlbGV0ZUxvcmVib29rKGJvb2tOYW1lKTtcbiAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgIGF3YWl0IGxvYWRBbGxEYXRhKCk7XG4gIH1cbn07XG5cbi8vIC0tLSBMb3JlYm9vayBFbnRyeSBDUlVEIE9wZXJhdGlvbnMgLS0tXG5cbi8qKlxuICog5Zyo5oyH5a6a55qE5LiW55WM5Lmm5Lit5Yib5bu65LiA5Liq5paw5p2h55uu44CCXG4gKiBAcGFyYW0gYm9va05hbWUg5LiW55WM5Lmm5ZCN56ewXG4gKiBAcGFyYW0gZW50cnlEYXRhIOimgeWIm+W7uueahOadoeebrueahOaVsOaNrlxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlTG9yZWJvb2tFbnRyeSA9IGFzeW5jIChib29rTmFtZTogc3RyaW5nLCBlbnRyeURhdGE6IExvcmVib29rRW50cnkpID0+IHtcbiAgLy8gVGF2ZXJuIEFQSemcgOimgeS4gOS4quaVsOe7hFxuICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuY3JlYXRlTG9yZWJvb2tFbnRyaWVzKGJvb2tOYW1lLCBbZW50cnlEYXRhXSk7XG4gIGlmIChyZXN1bHQgIT09IG51bGwgJiYgcmVzdWx0Lmxlbmd0aCA+IDApIHtcbiAgICBjb25zdCBuZXdTdGF0ZSA9IGdldFN0YXRlKCk7XG4gICAgY29uc3QgbmV3RW50cnkgPSByZXN1bHRbMF07IC8vIEFQSeS8mui/lOWbnuWIm+W7uuWQjueahOadoeebru+8jOWMheWQq1VJRFxuICAgIGNvbnN0IGN1cnJlbnRFbnRyaWVzID0gbmV3U3RhdGUubG9yZWJvb2tFbnRyaWVzLmdldChib29rTmFtZSkgfHwgW107XG4gICAgbmV3U3RhdGUubG9yZWJvb2tFbnRyaWVzLnNldChib29rTmFtZSwgWy4uLmN1cnJlbnRFbnRyaWVzLCBuZXdFbnRyeV0pO1xuICAgIHVwZGF0ZVN0YXRlKHMgPT4gKHsgLi4ucywgbG9yZWJvb2tFbnRyaWVzOiBuZXcgTWFwKG5ld1N0YXRlLmxvcmVib29rRW50cmllcykgfSkpO1xuICB9XG59O1xuXG4vKipcbiAqIOabtOaWsOS4lueVjOS5puS4reeahOS4gOS4queOsOacieadoeebruOAglxuICogQHBhcmFtIGJvb2tOYW1lIOS4lueVjOS5puWQjeensFxuICogQHBhcmFtIHVpZCDopoHmm7TmlrDnmoTmnaHnm67nmoTllK/kuIBJRFxuICogQHBhcmFtIHVwZGF0ZXMg6KaB5bqU55So5LqO5p2h55uu55qE6YOo5YiG5pu05pawXG4gKi9cbmV4cG9ydCBjb25zdCB1cGRhdGVMb3JlYm9va0VudHJ5ID0gYXN5bmMgKGJvb2tOYW1lOiBzdHJpbmcsIHVpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPExvcmVib29rRW50cnk+KSA9PiB7XG4gIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgY29uc3QgZW50cmllcyA9IHN0YXRlLmxvcmVib29rRW50cmllcy5nZXQoYm9va05hbWUpO1xuICBpZiAoIWVudHJpZXMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYFtXSU8gQ29yZV0gQm9vayBub3QgZm91bmQgaW4gc3RhdGU6ICR7Ym9va05hbWV9YCk7XG4gIH1cblxuICBsZXQgZW50cnlVcGRhdGVkID0gZmFsc2U7XG4gIGNvbnN0IHVwZGF0ZWRFbnRyaWVzID0gZW50cmllcy5tYXAoZW50cnkgPT4ge1xuICAgIGlmIChlbnRyeS51aWQgPT09IHVpZCkge1xuICAgICAgZW50cnlVcGRhdGVkID0gdHJ1ZTtcbiAgICAgIHJldHVybiB7IC4uLmVudHJ5LCAuLi51cGRhdGVzIH07XG4gICAgfVxuICAgIHJldHVybiBlbnRyeTtcbiAgfSk7XG5cbiAgaWYgKGVudHJ5VXBkYXRlZCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5zZXRMb3JlYm9va0VudHJpZXMoYm9va05hbWUsIHVwZGF0ZWRFbnRyaWVzKTtcbiAgICBpZiAocmVzdWx0ICE9PSBudWxsKSB7XG4gICAgICBzdGF0ZS5sb3JlYm9va0VudHJpZXMuc2V0KGJvb2tOYW1lLCB1cGRhdGVkRW50cmllcyk7XG4gICAgICB1cGRhdGVTdGF0ZShzID0+ICh7IC4uLnMsIGxvcmVib29rRW50cmllczogbmV3IE1hcChzdGF0ZS5sb3JlYm9va0VudHJpZXMpIH0pKTtcbiAgICB9XG4gIH1cbn07XG5cbi8qKlxuICog5LuO5LiW55WM5Lmm5Lit5Yig6Zmk5LiA5Liq5p2h55uu44CCXG4gKiBAcGFyYW0gYm9va05hbWUg5LiW55WM5Lmm5ZCN56ewXG4gKiBAcGFyYW0gdWlkIOimgeWIoOmZpOeahOadoeebrueahOWUr+S4gElEXG4gKi9cbmV4cG9ydCBjb25zdCBkZWxldGVMb3JlYm9va0VudHJ5ID0gYXN5bmMgKGJvb2tOYW1lOiBzdHJpbmcsIHVpZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5kZWxldGVMb3JlYm9va0VudHJpZXMoYm9va05hbWUsIFt1aWRdKTtcbiAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgICBjb25zdCBlbnRyaWVzID0gc3RhdGUubG9yZWJvb2tFbnRyaWVzLmdldChib29rTmFtZSkgfHwgW107XG4gICAgY29uc3QgZmlsdGVyZWRFbnRyaWVzID0gZW50cmllcy5maWx0ZXIoZW50cnkgPT4gZW50cnkudWlkICE9PSB1aWQpO1xuICAgIHN0YXRlLmxvcmVib29rRW50cmllcy5zZXQoYm9va05hbWUsIGZpbHRlcmVkRW50cmllcyk7XG4gICAgdXBkYXRlU3RhdGUocyA9PiAoeyAuLi5zLCBsb3JlYm9va0VudHJpZXM6IG5ldyBNYXAoc3RhdGUubG9yZWJvb2tFbnRyaWVzKSB9KSk7XG4gIH1cbn07XG5cbi8vIC0tLSBHbG9iYWwgU2V0dGluZ3MgLS0tXG5cbi8qKlxuICog6K6+572u5LiA5Liq5YWo5bGA5LiW55WM5Lmm55qE5ZCv55So54q25oCB44CCXG4gKiBAcGFyYW0gYm9va05hbWUg5LiW55WM5Lmm55qE5ZCN56ewXG4gKiBAcGFyYW0gZW5hYmxlZCB0cnVl5Li65ZCv55SoLCBmYWxzZeS4uuemgeeUqFxuICovXG5leHBvcnQgY29uc3Qgc2V0R2xvYmFsTG9yZWJvb2tFbmFibGVkID0gYXN5bmMgKGJvb2tOYW1lOiBzdHJpbmcsIGVuYWJsZWQ6IGJvb2xlYW4pID0+IHtcbiAgY29uc3Qgc2V0dGluZ3MgPSAoYXdhaXQgVGF2ZXJuQVBJLmdldExvcmVib29rU2V0dGluZ3MoKSkgfHwge307XG4gIGlmICghc2V0dGluZ3Muc2VsZWN0ZWRfZ2xvYmFsX2xvcmVib29rcykge1xuICAgIHNldHRpbmdzLnNlbGVjdGVkX2dsb2JhbF9sb3JlYm9va3MgPSBbXTtcbiAgfVxuXG4gIGNvbnN0IGVuYWJsZWRCb29rcyA9IG5ldyBTZXQoc2V0dGluZ3Muc2VsZWN0ZWRfZ2xvYmFsX2xvcmVib29rcyk7XG4gIGlmIChlbmFibGVkKSB7XG4gICAgZW5hYmxlZEJvb2tzLmFkZChib29rTmFtZSk7XG4gIH0gZWxzZSB7XG4gICAgZW5hYmxlZEJvb2tzLmRlbGV0ZShib29rTmFtZSk7XG4gIH1cbiAgc2V0dGluZ3Muc2VsZWN0ZWRfZ2xvYmFsX2xvcmVib29rcyA9IEFycmF5LmZyb20oZW5hYmxlZEJvb2tzKTtcblxuICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuc2V0TG9yZWJvb2tTZXR0aW5ncyhzZXR0aW5ncyk7XG4gIGlmIChyZXN1bHQgIT09IG51bGwpIHtcbiAgICAvLyDmm7TmlrDmnKzlnLDnirbmgIHku6Xnq4vljbPlj43mmKBVSeWPmOWMllxuICAgIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgICBjb25zdCBib29rID0gc3RhdGUuYWxsTG9yZWJvb2tzLmZpbmQoYiA9PiBiLm5hbWUgPT09IGJvb2tOYW1lKTtcbiAgICBpZiAoYm9vaykge1xuICAgICAgYm9vay5lbmFibGVkID0gZW5hYmxlZDtcbiAgICAgIHVwZGF0ZVN0YXRlKHMgPT4gKHsgLi4ucywgYWxsTG9yZWJvb2tzOiBbLi4uc3RhdGUuYWxsTG9yZWJvb2tzXSB9KSk7XG4gICAgfVxuICB9XG59O1xuXG4vLyAtLS0gUmVnZXggQ1JVRCBPcGVyYXRpb25zIC0tLVxuXG4vKipcbiAqIOaPkOS6pOato+WImeihqOi+vuW8j+WIl+ihqOeahOabtOaUueOAglxuICogQHBhcmFtIHVwZGF0ZWRMaXN0IOimgeaPkOS6pOeahOWujOaVtOato+WImeihqOi+vuW8j+WIl+ihqCAo5LuF6ZmQVUnmnaXmupApXG4gKi9cbmNvbnN0IGNvbW1pdFJlZ2V4Q2hhbmdlcyA9IGFzeW5jICh1cGRhdGVkTGlzdDogVGF2ZXJuUmVnZXhbXSkgPT4ge1xuICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkucmVwbGFjZVJlZ2V4ZXModXBkYXRlZExpc3QpO1xuICBpZiAocmVzdWx0ICE9PSBudWxsKSB7XG4gICAgLy8g5oiQ5Yqf5ZCO77yM6YeN5paw5Yqg6L295omA5pyJ5pWw5o2u5Lul56Gu5L+d5LiO5ZCO56uv5a6M5YWo5ZCM5q2lXG4gICAgLy8g6L+Z5piv5pyA566A5Y2V5Y+v6Z2g55qE5pa55byP77yM6YG/5YWN5aSN5p2C55qE5pys5Zyw54q25oCB566h55CGXG4gICAgYXdhaXQgbG9hZEFsbERhdGEoKTtcbiAgfVxufTtcblxuLyoqXG4gKiDliJvlu7rkuIDkuKrmlrDnmoTmraPliJnooajovr7lvI/jgIJcbiAqIEBwYXJhbSBuZXdSZWdleERhdGEg6KaB5Yib5bu655qE5q2j5YiZ6KGo6L6+5byP55qE5pWw5o2uXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVSZWdleCA9IGFzeW5jIChuZXdSZWdleERhdGE6IFBhcnRpYWw8VGF2ZXJuUmVnZXg+KSA9PiB7XG4gIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgLy8g5Y+q5aSE55CGVUnmnaXmupDnmoTmraPliJlcbiAgY29uc3QgY3VycmVudFVJUmVnZXhlcyA9IFtcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmdsb2JhbC5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmNoYXJhY3Rlci5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgXTtcblxuICBjb25zdCBmaW5hbE5ld1JlZ2V4OiBUYXZlcm5SZWdleCA9IHtcbiAgICBpZDogYHVpLSR7RGF0ZS5ub3coKX1gLFxuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgc291cmNlOiAndWknLFxuICAgIC4uLm5ld1JlZ2V4RGF0YSxcbiAgfSBhcyBUYXZlcm5SZWdleDtcblxuICBhd2FpdCBjb21taXRSZWdleENoYW5nZXMoWy4uLmN1cnJlbnRVSVJlZ2V4ZXMsIGZpbmFsTmV3UmVnZXhdKTtcbn07XG5cbi8qKlxuICog5pu05paw5LiA5Liq546w5pyJ55qE5q2j5YiZ6KGo6L6+5byP44CCXG4gKiBAcGFyYW0gcmVnZXhJZCDopoHmm7TmlrDnmoTmraPliJnooajovr7lvI/nmoRJRFxuICogQHBhcmFtIHVwZGF0ZXMg6KaB5bqU55So55qE5pu05pawXG4gKi9cbmV4cG9ydCBjb25zdCB1cGRhdGVSZWdleCA9IGFzeW5jIChyZWdleElkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VGF2ZXJuUmVnZXg+KSA9PiB7XG4gIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgY29uc3QgY3VycmVudFVJUmVnZXhlcyA9IFtcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmdsb2JhbC5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmNoYXJhY3Rlci5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgXTtcblxuICBjb25zdCB1cGRhdGVkTGlzdCA9IGN1cnJlbnRVSVJlZ2V4ZXMubWFwKHIgPT4gKHIuaWQgPT09IHJlZ2V4SWQgPyB7IC4uLnIsIC4uLnVwZGF0ZXMgfSA6IHIpKTtcblxuICBhd2FpdCBjb21taXRSZWdleENoYW5nZXModXBkYXRlZExpc3QpO1xufTtcblxuLyoqXG4gKiDliKDpmaTkuIDkuKrmraPliJnooajovr7lvI/jgIJcbiAqIEBwYXJhbSByZWdleElkIOimgeWIoOmZpOeahOato+WImeihqOi+vuW8j+eahElEXG4gKi9cbmV4cG9ydCBjb25zdCBkZWxldGVSZWdleCA9IGFzeW5jIChyZWdleElkOiBzdHJpbmcpID0+IHtcbiAgY29uc3Qgc3RhdGUgPSBnZXRTdGF0ZSgpO1xuICBjb25zdCBjdXJyZW50VUlSZWdleGVzID0gW1xuICAgIC4uLnN0YXRlLnJlZ2V4ZXMuZ2xvYmFsLmZpbHRlcihyID0+IHIuc291cmNlICE9PSAnY2FyZCcpLFxuICAgIC4uLnN0YXRlLnJlZ2V4ZXMuY2hhcmFjdGVyLmZpbHRlcihyID0+IHIuc291cmNlICE9PSAnY2FyZCcpLFxuICBdO1xuXG4gIGNvbnN0IHVwZGF0ZWRMaXN0ID0gY3VycmVudFVJUmVnZXhlcy5maWx0ZXIociA9PiByLmlkICE9PSByZWdleElkKTtcblxuICBhd2FpdCBjb21taXRSZWdleENoYW5nZXModXBkYXRlZExpc3QpO1xufTtcblxuLy8gLS0tIEJ1bGsgT3BlcmF0aW9ucyAtLS1cblxuLyoqXG4gKiDmiafooYzmibnph4/mkJzntKLlkozmm7/mjaLmk43kvZzjgIJcbiAqIEBwYXJhbSBzZWFyY2hUZXJtIOimgeaQnOe0oueahOivjeivrVxuICogQHBhcmFtIHJlcGxhY2VUZXJtIOimgeabv+aNouaIkOeahOivjeivrVxuICogQHJldHVybnMg6L+U5Zue6KKr5L+u5pS555qE5p2h55uu5pWw6YePXG4gKi9cbmV4cG9ydCBjb25zdCBwZXJmb3JtQnVsa1JlcGxhY2UgPSBhc3luYyAoc2VhcmNoVGVybTogc3RyaW5nLCByZXBsYWNlVGVybTogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgaWYgKCFzZWFyY2hUZXJtLnRyaW0oKSkge1xuICAgIHRocm93IG5ldyBFcnJvcign5pCc57Si6K+N5LiN6IO95Li656m6Jyk7XG4gIH1cblxuICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gIGxldCBtb2RpZmllZENvdW50ID0gMDtcbiAgY29uc3QgbW9kaWZpZWRCb29rcyA9IG5ldyBNYXA8c3RyaW5nLCBMb3JlYm9va0VudHJ5W10+KCk7XG5cbiAgLy8g5o+Q5YmN5Yib5bu65q2j5YiZ6KGo6L6+5byP5Lul5o+Q6auY5oCn6IO9XG4gIGNvbnN0IHNlYXJjaFJlZ2V4ID0gbmV3IFJlZ0V4cChzZWFyY2hUZXJtLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCAnXFxcXCQmJyksICdnaScpO1xuXG4gIC8vIOmBjeWOhuaJgOacieS4lueVjOS5puWSjOadoeebrlxuICBmb3IgKGNvbnN0IFtib29rTmFtZSwgZW50cmllc10gb2Ygc3RhdGUubG9yZWJvb2tFbnRyaWVzLmVudHJpZXMoKSkge1xuICAgIGNvbnN0IG1vZGlmaWVkRW50cmllczogTG9yZWJvb2tFbnRyeVtdID0gW107XG4gICAgbGV0IGJvb2tIYXNDaGFuZ2VzID0gZmFsc2U7XG5cbiAgICBmb3IgKGNvbnN0IGVudHJ5IG9mIGVudHJpZXMpIHtcbiAgICAgIGxldCBlbnRyeU1vZGlmaWVkID0gZmFsc2U7XG4gICAgICBjb25zdCB1cGRhdGVkRW50cnkgPSB7IC4uLmVudHJ5IH07XG5cbiAgICAgIC8vIOabv+aNouadoeebruWQjeensCAoY29tbWVudClcbiAgICAgIGlmICh1cGRhdGVkRW50cnkuY29tbWVudCkge1xuICAgICAgICBjb25zdCBuZXdDb21tZW50ID0gdXBkYXRlZEVudHJ5LmNvbW1lbnQucmVwbGFjZShzZWFyY2hSZWdleCwgcmVwbGFjZVRlcm0pO1xuICAgICAgICBpZiAobmV3Q29tbWVudCAhPT0gdXBkYXRlZEVudHJ5LmNvbW1lbnQpIHtcbiAgICAgICAgICB1cGRhdGVkRW50cnkuY29tbWVudCA9IG5ld0NvbW1lbnQ7XG4gICAgICAgICAgZW50cnlNb2RpZmllZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5pu/5o2i5YWz6ZSu6K+NXG4gICAgICBpZiAodXBkYXRlZEVudHJ5LmtleXMgJiYgdXBkYXRlZEVudHJ5LmtleXMubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBvcmlnaW5hbEtleXMgPSBKU09OLnN0cmluZ2lmeSh1cGRhdGVkRW50cnkua2V5cyk7XG4gICAgICAgIHVwZGF0ZWRFbnRyeS5rZXlzID0gdXBkYXRlZEVudHJ5LmtleXMubWFwKGtleSA9PiBrZXkucmVwbGFjZShzZWFyY2hSZWdleCwgcmVwbGFjZVRlcm0pKTtcbiAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRFbnRyeS5rZXlzKSAhPT0gb3JpZ2luYWxLZXlzKSB7XG4gICAgICAgICAgZW50cnlNb2RpZmllZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5pu/5o2i5YaF5a65XG4gICAgICBpZiAodXBkYXRlZEVudHJ5LmNvbnRlbnQpIHtcbiAgICAgICAgY29uc3QgbmV3Q29udGVudCA9IHVwZGF0ZWRFbnRyeS5jb250ZW50LnJlcGxhY2Uoc2VhcmNoUmVnZXgsIHJlcGxhY2VUZXJtKTtcbiAgICAgICAgaWYgKG5ld0NvbnRlbnQgIT09IHVwZGF0ZWRFbnRyeS5jb250ZW50KSB7XG4gICAgICAgICAgdXBkYXRlZEVudHJ5LmNvbnRlbnQgPSBuZXdDb250ZW50O1xuICAgICAgICAgIGVudHJ5TW9kaWZpZWQgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChlbnRyeU1vZGlmaWVkKSB7XG4gICAgICAgIG1vZGlmaWVkQ291bnQrKztcbiAgICAgICAgYm9va0hhc0NoYW5nZXMgPSB0cnVlO1xuICAgICAgfVxuXG4gICAgICBtb2RpZmllZEVudHJpZXMucHVzaCh1cGRhdGVkRW50cnkpO1xuICAgIH1cblxuICAgIGlmIChib29rSGFzQ2hhbmdlcykge1xuICAgICAgbW9kaWZpZWRCb29rcy5zZXQoYm9va05hbWUsIG1vZGlmaWVkRW50cmllcyk7XG4gICAgfVxuICB9XG5cbiAgLy8g5bm26KGM5L+d5a2Y5omA5pyJ5L+u5pS56L+H55qE5LiW55WM5LmmXG4gIGNvbnN0IHVwZGF0ZVByb21pc2VzID0gQXJyYXkuZnJvbShtb2RpZmllZEJvb2tzLmVudHJpZXMoKSkubWFwKGFzeW5jIChbYm9va05hbWUsIGVudHJpZXNdKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLnNldExvcmVib29rRW50cmllcyhib29rTmFtZSwgZW50cmllcyk7XG4gICAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgICAgLy8g5ZyoIFByb21pc2Ug5YaF6YOo5YeG5aSH54q25oCB5pu05paw77yM5L2G5LiN55u05o6l5L+u5pS5XG4gICAgICByZXR1cm4gKCkgPT4gc3RhdGUubG9yZWJvb2tFbnRyaWVzLnNldChib29rTmFtZSwgZW50cmllcyk7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9KTtcblxuICBjb25zdCBzdGF0ZVVwZGF0ZXJzID0gKGF3YWl0IFByb21pc2UuYWxsKHVwZGF0ZVByb21pc2VzKSkuZmlsdGVyKEJvb2xlYW4pO1xuICBzdGF0ZVVwZGF0ZXJzLmZvckVhY2godXBkYXRlID0+IHVwZGF0ZSEoKSk7XG5cbiAgaWYgKG1vZGlmaWVkQm9va3Muc2l6ZSA+IDApIHtcbiAgICB1cGRhdGVTdGF0ZShzID0+ICh7IC4uLnMsIGxvcmVib29rRW50cmllczogbmV3IE1hcChzdGF0ZS5sb3JlYm9va0VudHJpZXMpIH0pKTtcbiAgfVxuXG4gIHJldHVybiBtb2RpZmllZENvdW50O1xufTtcblxuLyoqXG4gKiDmibnph4/liKDpmaTpgInkuK3nmoTmnaHnm67jgIJcbiAqIEByZXR1cm5zIOi/lOWbnuWIoOmZpOeahOadoeebruaVsOmHj1xuICovXG5leHBvcnQgY29uc3QgcGVyZm9ybUJ1bGtEZWxldGUgPSBhc3luYyAoKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgY29uc3Qgc3RhdGUgPSBnZXRTdGF0ZSgpO1xuICBjb25zdCBzZWxlY3RlZEl0ZW1zID0gQXJyYXkuZnJvbShzdGF0ZS5zZWxlY3RlZEl0ZW1zKTtcblxuICBpZiAoc2VsZWN0ZWRJdGVtcy5sZW5ndGggPT09IDApIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ+ayoeaciemAieS4reS7u+S9leadoeebricpO1xuICB9XG5cbiAgbGV0IGRlbGV0ZWRDb3VudCA9IDA7XG4gIGNvbnN0IGJvb2tzVG9VcGRhdGUgPSBuZXcgTWFwPHN0cmluZywgc3RyaW5nW10+KCk7IC8vIGJvb2tOYW1lIC0+IHVpZHMgdG8gZGVsZXRlXG5cbiAgLy8g6Kej5p6Q6YCJ5Lit55qE5p2h55uuXG4gIGZvciAoY29uc3QgaXRlbUlkIG9mIHNlbGVjdGVkSXRlbXMpIHtcbiAgICBjb25zdCBbYm9va05hbWUsIHVpZF0gPSBpdGVtSWQuc3BsaXQoJy8nKTtcbiAgICBpZiAoYm9va05hbWUgJiYgdWlkKSB7XG4gICAgICBpZiAoIWJvb2tzVG9VcGRhdGUuaGFzKGJvb2tOYW1lKSkge1xuICAgICAgICBib29rc1RvVXBkYXRlLnNldChib29rTmFtZSwgW10pO1xuICAgICAgfVxuICAgICAgYm9va3NUb1VwZGF0ZS5nZXQoYm9va05hbWUpPy5wdXNoKHVpZCk7XG4gICAgfVxuICB9XG5cbiAgLy8g5bm26KGM5Yig6Zmk5q+P5Liq5LiW55WM5Lmm5Lit55qE5p2h55uuXG4gIGNvbnN0IGRlbGV0ZVByb21pc2VzID0gQXJyYXkuZnJvbShib29rc1RvVXBkYXRlLmVudHJpZXMoKSkubWFwKGFzeW5jIChbYm9va05hbWUsIHVpZHNUb0RlbGV0ZV0pID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuZGVsZXRlTG9yZWJvb2tFbnRyaWVzKGJvb2tOYW1lLCB1aWRzVG9EZWxldGUpO1xuICAgIGlmIChyZXN1bHQgIT09IG51bGwpIHtcbiAgICAgIHJldHVybiB7IGJvb2tOYW1lLCB1aWRzVG9EZWxldGUsIGNvdW50OiB1aWRzVG9EZWxldGUubGVuZ3RoIH07XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9KTtcblxuICBjb25zdCBkZWxldGVSZXN1bHRzID0gKGF3YWl0IFByb21pc2UuYWxsKGRlbGV0ZVByb21pc2VzKSkuZmlsdGVyKEJvb2xlYW4pO1xuXG4gIGZvciAoY29uc3QgcmVzdWx0IG9mIGRlbGV0ZVJlc3VsdHMpIHtcbiAgICBpZiAoIXJlc3VsdCkgY29udGludWU7XG4gICAgZGVsZXRlZENvdW50ICs9IHJlc3VsdC5jb3VudDtcbiAgICAvLyDmm7TmlrDmnKzlnLDnirbmgIFcbiAgICBjb25zdCBlbnRyaWVzID0gc3RhdGUubG9yZWJvb2tFbnRyaWVzLmdldChyZXN1bHQuYm9va05hbWUpIHx8IFtdO1xuICAgIGNvbnN0IGZpbHRlcmVkRW50cmllcyA9IGVudHJpZXMuZmlsdGVyKGVudHJ5ID0+ICFyZXN1bHQudWlkc1RvRGVsZXRlLmluY2x1ZGVzKGVudHJ5LnVpZCkpO1xuICAgIHN0YXRlLmxvcmVib29rRW50cmllcy5zZXQocmVzdWx0LmJvb2tOYW1lLCBmaWx0ZXJlZEVudHJpZXMpO1xuICB9XG5cbiAgLy8g5riF56m66YCJ5Lit54q25oCBXG4gIHN0YXRlLnNlbGVjdGVkSXRlbXMuY2xlYXIoKTtcbiAgdXBkYXRlU3RhdGUocyA9PiAoe1xuICAgIC4uLnMsXG4gICAgbG9yZWJvb2tFbnRyaWVzOiBuZXcgTWFwKHN0YXRlLmxvcmVib29rRW50cmllcyksXG4gICAgc2VsZWN0ZWRJdGVtczogbmV3IFNldCgpLFxuICAgIG11bHRpU2VsZWN0TW9kZTogZmFsc2UsXG4gIH0pKTtcblxuICByZXR1cm4gZGVsZXRlZENvdW50O1xufTtcblxuLyoqXG4gKiDmibnph4/lkK/nlKjpgInkuK3nmoTmnaHnm67jgIJcbiAqIEByZXR1cm5zIOi/lOWbnuWQr+eUqOeahOadoeebruaVsOmHj1xuICovXG5leHBvcnQgY29uc3QgcGVyZm9ybUJ1bGtFbmFibGUgPSBhc3luYyAoKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgcmV0dXJuIGF3YWl0IHBlcmZvcm1CdWxrVG9nZ2xlKHRydWUpO1xufTtcblxuLyoqXG4gKiDmibnph4/npoHnlKjpgInkuK3nmoTmnaHnm67jgIJcbiAqIEByZXR1cm5zIOi/lOWbnuemgeeUqOeahOadoeebruaVsOmHj1xuICovXG5leHBvcnQgY29uc3QgcGVyZm9ybUJ1bGtEaXNhYmxlID0gYXN5bmMgKCk6IFByb21pc2U8bnVtYmVyPiA9PiB7XG4gIHJldHVybiBhd2FpdCBwZXJmb3JtQnVsa1RvZ2dsZShmYWxzZSk7XG59O1xuXG4vKipcbiAqIOaJuemHj+WIh+aNouadoeebrueahOWQr+eUqC/npoHnlKjnirbmgIHjgIJcbiAqIEBwYXJhbSBlbmFibGVkIOebruagh+eKtuaAgVxuICogQHJldHVybnMg6L+U5Zue5L+u5pS555qE5p2h55uu5pWw6YePXG4gKi9cbmNvbnN0IHBlcmZvcm1CdWxrVG9nZ2xlID0gYXN5bmMgKGVuYWJsZWQ6IGJvb2xlYW4pOiBQcm9taXNlPG51bWJlcj4gPT4ge1xuICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gIGNvbnN0IHNlbGVjdGVkSXRlbXMgPSBBcnJheS5mcm9tKHN0YXRlLnNlbGVjdGVkSXRlbXMpO1xuXG4gIGlmIChzZWxlY3RlZEl0ZW1zLmxlbmd0aCA9PT0gMCkge1xuICAgIHRocm93IG5ldyBFcnJvcign5rKh5pyJ6YCJ5Lit5Lu75L2V5p2h55uuJyk7XG4gIH1cblxuICBsZXQgbW9kaWZpZWRDb3VudCA9IDA7XG4gIGNvbnN0IGJvb2tzVG9VcGRhdGUgPSBuZXcgTWFwPHN0cmluZywgTG9yZWJvb2tFbnRyeVtdPigpO1xuXG4gIC8vIOino+aekOmAieS4reeahOadoeebruW5tuWHhuWkh+abtOaWsFxuICBmb3IgKGNvbnN0IGl0ZW1JZCBvZiBzZWxlY3RlZEl0ZW1zKSB7XG4gICAgY29uc3QgW2Jvb2tOYW1lLCB1aWRdID0gaXRlbUlkLnNwbGl0KCcvJyk7XG4gICAgaWYgKGJvb2tOYW1lICYmIHVpZCkge1xuICAgICAgY29uc3QgZW50cmllcyA9IHN0YXRlLmxvcmVib29rRW50cmllcy5nZXQoYm9va05hbWUpIHx8IFtdO1xuICAgICAgY29uc3QgdXBkYXRlZEVudHJpZXMgPSBlbnRyaWVzLm1hcChlbnRyeSA9PiB7XG4gICAgICAgIGlmIChlbnRyeS51aWQgPT09IHVpZCAmJiBlbnRyeS5lbmFibGVkICE9PSBlbmFibGVkKSB7XG4gICAgICAgICAgbW9kaWZpZWRDb3VudCsrO1xuICAgICAgICAgIHJldHVybiB7IC4uLmVudHJ5LCBlbmFibGVkIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGVudHJ5O1xuICAgICAgfSk7XG5cbiAgICAgIGlmICh1cGRhdGVkRW50cmllcy5zb21lKChlbnRyeSwgaW5kZXgpID0+IGVudHJ5ICE9PSBlbnRyaWVzW2luZGV4XSkpIHtcbiAgICAgICAgYm9va3NUb1VwZGF0ZS5zZXQoYm9va05hbWUsIHVwZGF0ZWRFbnRyaWVzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDlubbooYzkv53lrZjmiYDmnInkv67mlLnov4fnmoTkuJbnlYzkuaZcbiAgY29uc3QgdXBkYXRlUHJvbWlzZXMgPSBBcnJheS5mcm9tKGJvb2tzVG9VcGRhdGUuZW50cmllcygpKS5tYXAoYXN5bmMgKFtib29rTmFtZSwgZW50cmllc10pID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuc2V0TG9yZWJvb2tFbnRyaWVzKGJvb2tOYW1lLCBlbnRyaWVzKTtcbiAgICBpZiAocmVzdWx0ICE9PSBudWxsKSB7XG4gICAgICByZXR1cm4gKCkgPT4gc3RhdGUubG9yZWJvb2tFbnRyaWVzLnNldChib29rTmFtZSwgZW50cmllcyk7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9KTtcblxuICBjb25zdCBzdGF0ZVVwZGF0ZXJzID0gKGF3YWl0IFByb21pc2UuYWxsKHVwZGF0ZVByb21pc2VzKSkuZmlsdGVyKEJvb2xlYW4pO1xuICBzdGF0ZVVwZGF0ZXJzLmZvckVhY2godXBkYXRlID0+IHVwZGF0ZSEoKSk7XG5cbiAgLy8g5riF56m66YCJ5Lit54q25oCB5bm26YCA5Ye65aSa6YCJ5qih5byPXG4gIHN0YXRlLnNlbGVjdGVkSXRlbXMuY2xlYXIoKTtcbiAgdXBkYXRlU3RhdGUocyA9PiAoe1xuICAgIC4uLnMsXG4gICAgbG9yZWJvb2tFbnRyaWVzOiBuZXcgTWFwKHN0YXRlLmxvcmVib29rRW50cmllcyksXG4gICAgc2VsZWN0ZWRJdGVtczogbmV3IFNldCgpLFxuICAgIG11bHRpU2VsZWN0TW9kZTogZmFsc2UsXG4gIH0pKTtcblxuICByZXR1cm4gbW9kaWZpZWRDb3VudDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/core.ts\n\n}");
  },
  "./src/world_info_optimizer/events.ts": 
  /*!********************************************!*\
  !*** ./src/world_info_optimizer/events.ts ***!
  \********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeEventHandlers: () => (/* binding */ initializeEventHandlers)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core */ \"./src/world_info_optimizer/core.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _ui_modals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/modals */ \"./src/world_info_optimizer/ui/modals.ts\");\n// src/world_info_optimizer/events.ts\n\n\n\n\nlet parentDoc;\nlet $;\n/**\n * 初始化所有UI事件处理器。\n * 这个函数应该在UI注入DOM后调用。\n * @param parentWindow 父窗口对象\n */\nconst initializeEventHandlers = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    const $body = $('body', parentDoc);\n    // --- 主面板和按钮的事件 ---\n    // 打开面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeIn(200);\n        // 首次打开时加载数据\n        if (!(0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)().isDataLoaded) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n        }\n    });\n    // 关闭面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} .wio-close-btn`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeOut(200);\n    });\n    // --- 事件委托：主面板内的所有点击事件 ---\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    $panel.on('click', async (event) => {\n        const $target = $(event.target);\n        // --- 标签页切换 ---\n        const tabButton = $target.closest('.wio-tab-btn');\n        if (tabButton.length) {\n            const tabId = tabButton.data('tab-id');\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.setActiveTab)(tabId);\n            return;\n        }\n        // --- 工具栏操作 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.CREATE_LOREBOOK_BTN_ID}`).length) {\n            handleCreateBook();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}`).length || $target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}-retry`).length) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n            return;\n        }\n        // --- 批量操作 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_REPLACE_BTN_ID}`).length) {\n            handleBulkReplace();\n            return;\n        }\n        // --- 多选模式 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.MULTI_SELECT_BTN_ID}`).length) {\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.toggleMultiSelectMode)();\n            return;\n        }\n        // --- 批量操作按钮 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_DELETE_BTN_ID}`).length) {\n            handleBulkDelete();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_ENABLE_BTN_ID}`).length) {\n            handleBulkEnable();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_DISABLE_BTN_ID}`).length) {\n            handleBulkDisable();\n            return;\n        }\n        // --- 折叠/展开操作 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.COLLAPSE_ALL_BTN_ID}`).length) {\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.collapseAllBooks)();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.EXPAND_ALL_BTN_ID}`).length) {\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.expandAllBooks)();\n            return;\n        }\n        // --- 世界书折叠切换 ---\n        const collapseToggle = $target.closest('.wio-collapse-toggle');\n        if (collapseToggle.length) {\n            const bookName = collapseToggle.closest('.wio-book-group').data('book-name');\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.toggleBookCollapse)(bookName);\n            return;\n        }\n        // --- 世界书操作 ---\n        const renameBookBtn = $target.closest('.wio-rename-book-btn');\n        if (renameBookBtn.length) {\n            const bookName = renameBookBtn.closest('.wio-book-group').data('book-name');\n            handleRenameBook(bookName);\n            return;\n        }\n        const deleteBookBtn = $target.closest('.wio-delete-book-btn');\n        if (deleteBookBtn.length) {\n            const bookName = deleteBookBtn.closest('.wio-book-group').data('book-name');\n            handleDeleteBook(bookName);\n            return;\n        }\n        // --- 条目操作 ---\n        const createEntryBtn = $target.closest('.wio-create-entry-btn');\n        if (createEntryBtn.length) {\n            const bookName = createEntryBtn.data('book-name');\n            handleCreateEntry(bookName);\n            return;\n        }\n        const editEntryBtn = $target.closest('.wio-edit-entry-btn');\n        if (editEntryBtn.length) {\n            const entryItem = editEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleEditEntry(bookName, uid);\n            return;\n        }\n        const deleteEntryBtn = $target.closest('.wio-delete-entry-btn');\n        if (deleteEntryBtn.length) {\n            const entryItem = deleteEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleDeleteEntry(bookName, uid);\n            return;\n        }\n        // --- 正则操作 ---\n        const createRegexBtn = $target.closest('.wio-create-regex-btn');\n        if (createRegexBtn.length) {\n            const scope = createRegexBtn.data('scope');\n            handleCreateRegex(scope);\n            return;\n        }\n        const editRegexBtn = $target.closest('.wio-edit-regex-btn');\n        if (editRegexBtn.length) {\n            const regexItem = editRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleEditRegex(regexId);\n            return;\n        }\n        const deleteRegexBtn = $target.closest('.wio-delete-regex-btn');\n        if (deleteRegexBtn.length) {\n            const regexItem = deleteRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleDeleteRegex(regexId);\n            return;\n        }\n    });\n    // --- 事件委托：处理表单元素的 change 事件 ---\n    $panel.on('change', async (event) => {\n        const $target = $(event.target);\n        // --- 全局世界书启用/禁用切换 ---\n        if ($target.is('.wio-global-book-toggle')) {\n            const bookName = $target.closest('.wio-book-group').data('book-name');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.setGlobalLorebookEnabled)(bookName, isEnabled);\n            return;\n        }\n        // --- 条目启用/禁用切换 ---\n        if ($target.is('.wio-entry-toggle')) {\n            const entryItem = $target.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateLorebookEntry)(bookName, uid, { enabled: isEnabled });\n            return;\n        }\n        // --- 正则启用/禁用切换 ---\n        if ($target.is('.wio-regex-toggle')) {\n            const regexItem = $target.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateRegex)(regexId, { enabled: isEnabled });\n            return;\n        }\n    });\n    // --- 事件委托：处理搜索框输入 ---\n    $panel.on('input', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, event => {\n        const query = $(event.target).val();\n        (0,_store__WEBPACK_IMPORTED_MODULE_2__.setSearchQuery)(query);\n    });\n};\n// --- 事件处理器具体实现 ---\n/**\n * 处理重命名世界书的逻辑。\n * @param bookName 要重命名的世界书的当前名称\n */\nconst handleRenameBook = async (bookName) => {\n    try {\n        const newName = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '重命名世界书',\n            text: `为 \"${bookName}\" 输入新的名称:`,\n            value: bookName,\n        });\n        if (typeof newName === 'string' && newName.trim() && newName !== bookName) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.renameLorebook)(bookName, newName.trim());\n        }\n    }\n    catch (error) {\n        // 用户取消了输入\n        console.log('Rename operation cancelled.');\n    }\n};\n/**\n * 处理删除世界书的逻辑。\n * @param bookName 要删除的世界书的名称\n */\nconst handleDeleteBook = async (bookName) => {\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除世界书 \"${bookName}\" 吗？此操作无法撤销。`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebook)(bookName);\n        }\n    }\n    catch (error) {\n        // 用户取消了确认\n        console.log('Delete operation cancelled.');\n    }\n};\n/**\n * 处理创建新条目的逻辑。\n * @param bookName 新条目所属的世界书名称\n */\nconst handleCreateEntry = async (bookName) => {\n    try {\n        const newEntryData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { keys: [], content: '', comment: '' },\n            bookName,\n            isCreating: true,\n        });\n        // showEntryEditorModal 返回的 newEntryData 不包含 enabled 状态, 我们需要设置默认值\n        const entryToCreate = {\n            ...newEntryData,\n            enabled: true, // 新条目默认启用\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebookEntry)(bookName, entryToCreate);\n    }\n    catch (error) {\n        console.log('Create entry operation cancelled.');\n    }\n};\n/**\n * 处理编辑现有条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要编辑的条目的UID\n */\nconst handleEditEntry = async (bookName, uid) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const entry = state.lorebookEntries.get(bookName)?.find(e => e.uid === uid);\n    if (!entry) {\n        console.error(`Entry with UID ${uid} not found in book ${bookName}.`);\n        return;\n    }\n    try {\n        const updatedEntryData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { ...entry }, // 传入副本以防意外修改\n            bookName,\n            isCreating: false,\n        });\n        // 我们只需要更新发生变化的部分\n        const updates = {\n            comment: updatedEntryData.comment,\n            keys: updatedEntryData.keys,\n            content: updatedEntryData.content,\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateLorebookEntry)(bookName, uid, updates);\n    }\n    catch (error) {\n        console.log('Edit entry operation cancelled.');\n    }\n};\n/**\n * 处理删除条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要删除的条目的UID\n */\nconst handleDeleteEntry = async (bookName, uid) => {\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除这个条目吗？`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebookEntry)(bookName, uid);\n        }\n    }\n    catch (error) {\n        console.log('Delete entry operation cancelled.');\n    }\n};\n/**\n * 处理创建新世界书的逻辑。\n */\nconst handleCreateBook = async () => {\n    try {\n        const newName = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '创建新世界书',\n            text: '请输入新世界书的名称:',\n            value: 'New-Lorebook',\n        });\n        if (typeof newName === 'string' && newName.trim()) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebook)(newName.trim());\n        }\n    }\n    catch (error) {\n        console.log('Create lorebook operation cancelled.');\n    }\n};\n// --- 正则表达式事件处理器 ---\nconst handleCreateRegex = async (scope) => {\n    try {\n        const newRegexData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { script_name: '新正则', find_regex: '', replace_string: '' },\n            isCreating: true,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createRegex)({ ...newRegexData, scope });\n    }\n    catch (error) {\n        console.log('Create regex operation cancelled.');\n    }\n};\nconst handleEditRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (!regex) {\n        console.error(`Regex with ID ${regexId} not found.`);\n        return;\n    }\n    // 卡片内正则不可编辑\n    if (regex.source === 'card') {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法编辑来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const updatedRegexData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { ...regex },\n            isCreating: false,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateRegex)(regexId, updatedRegexData);\n    }\n    catch (error) {\n        console.log('Edit regex operation cancelled.');\n    }\n};\nconst handleDeleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (regex && regex.source === 'card') {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法删除来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: '你确定要永久删除这个正则表达式吗？',\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteRegex)(regexId);\n        }\n    }\n    catch (error) {\n        console.log('Delete regex operation cancelled.');\n    }\n};\n// --- 批量操作事件处理器 ---\n/**\n * 处理批量搜索替换操作。\n */\nconst handleBulkReplace = async () => {\n    const searchInput = document.getElementById(_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_SEARCH_INPUT_ID);\n    const replaceInput = document.getElementById(_constants__WEBPACK_IMPORTED_MODULE_0__.BULK_REPLACE_INPUT_ID);\n    if (!searchInput || !replaceInput) {\n        console.error('Bulk replace inputs not found');\n        return;\n    }\n    const searchTerm = searchInput.value.trim();\n    const replaceTerm = replaceInput.value.trim();\n    if (!searchTerm) {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '输入错误',\n            text: '请输入要搜索的内容。',\n        });\n        return;\n    }\n    try {\n        // 显示确认对话框\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认批量替换',\n            text: `你确定要将所有世界书条目中的 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n此操作将影响所有匹配的条目名称、关键词和内容。`,\n        });\n        if (confirmation) {\n            const modifiedCount = await (0,_core__WEBPACK_IMPORTED_MODULE_1__.performBulkReplace)(searchTerm, replaceTerm);\n            await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n                type: 'alert',\n                title: '替换完成',\n                text: `成功替换了 ${modifiedCount} 个条目中的内容。`,\n            });\n            // 清空输入框\n            searchInput.value = '';\n            replaceInput.value = '';\n        }\n    }\n    catch (error) {\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '替换失败',\n            text: `批量替换操作失败: ${errorMessage}`,\n        });\n    }\n};\n/**\n * 处理批量删除操作。\n */\nconst handleBulkDelete = async () => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const selectedCount = state.selectedItems.size;\n    if (selectedCount === 0) {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '没有选中项',\n            text: '请先选择要删除的条目。',\n        });\n        return;\n    }\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认批量删除',\n            text: `你确定要永久删除选中的 ${selectedCount} 个条目吗？此操作无法撤销。`,\n        });\n        if (confirmation) {\n            const deletedCount = await (0,_core__WEBPACK_IMPORTED_MODULE_1__.performBulkDelete)();\n            await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n                type: 'alert',\n                title: '删除完成',\n                text: `成功删除了 ${deletedCount} 个条目。`,\n            });\n        }\n    }\n    catch (error) {\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '删除失败',\n            text: `批量删除操作失败: ${errorMessage}`,\n        });\n    }\n};\n/**\n * 处理批量启用操作。\n */\nconst handleBulkEnable = async () => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const selectedCount = state.selectedItems.size;\n    if (selectedCount === 0) {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '没有选中项',\n            text: '请先选择要启用的条目。',\n        });\n        return;\n    }\n    try {\n        const modifiedCount = await (0,_core__WEBPACK_IMPORTED_MODULE_1__.performBulkEnable)();\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '启用完成',\n            text: `成功启用了 ${modifiedCount} 个条目。`,\n        });\n    }\n    catch (error) {\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '启用失败',\n            text: `批量启用操作失败: ${errorMessage}`,\n        });\n    }\n};\n/**\n * 处理批量禁用操作。\n */\nconst handleBulkDisable = async () => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const selectedCount = state.selectedItems.size;\n    if (selectedCount === 0) {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '没有选中项',\n            text: '请先选择要禁用的条目。',\n        });\n        return;\n    }\n    try {\n        const modifiedCount = await (0,_core__WEBPACK_IMPORTED_MODULE_1__.performBulkDisable)();\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '禁用完成',\n            text: `成功禁用了 ${modifiedCount} 个条目。`,\n        });\n    }\n    catch (error) {\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'alert',\n            title: '禁用失败',\n            text: `批量禁用操作失败: ${errorMessage}`,\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/events.ts\n\n}");
  },
  "./src/world_info_optimizer/index.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/index.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events */ \"./src/world_info_optimizer/events.ts\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ui */ \"./src/world_info_optimizer/ui.ts\");\n// src/world_info_optimizer/index.ts\n\n\n/**\n * 等待SillyTavern的核心DOM和API准备就绪。\n * @param callback 准备就绪后执行的回调函数\n */\nfunction onReady(callback) {\n    const domSelector = '#extensionsMenu';\n    const maxRetries = 100; // 等待最多约20秒\n    let retries = 0;\n    console.log('[WIO] Starting readiness check...');\n    const interval = setInterval(() => {\n        const parentWin = window.parent;\n        if (!parentWin) {\n            retries++;\n            return;\n        }\n        const domReady = parentWin.document.querySelector(domSelector) !== null;\n        const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n        if (domReady && apiReady) {\n            clearInterval(interval);\n            console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');\n            try {\n                callback(parentWin);\n            }\n            catch (e) {\n                console.error('[WIO] FATAL: Error during main callback execution.', e);\n            }\n        }\n        else {\n            retries++;\n            if (retries > maxRetries) {\n                clearInterval(interval);\n                console.error('[WIO] FATAL: Readiness check timed out.');\n                if (!domReady)\n                    console.error(`[WIO] -> Failure: DOM element \"${domSelector}\" not found.`);\n                if (!apiReady)\n                    console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!parentWin.TavernHelper}, jQuery: ${!!parentWin.jQuery}`);\n            }\n        }\n    }, 200);\n}\n/**\n * 应用程序的主函数。\n * @param parentWindow 父窗口对象\n */\nfunction main(parentWindow) {\n    console.log('[WIO] Initializing World Info Optimizer...');\n    // 1. 注入UI元素到DOM中\n    (0,_ui__WEBPACK_IMPORTED_MODULE_1__.injectUI)(parentWindow);\n    // 2. 初始化UI模块，使其订阅状态变化\n    (0,_ui__WEBPACK_IMPORTED_MODULE_1__.initUI)();\n    // 3. 绑定所有事件处理器\n    (0,_events__WEBPACK_IMPORTED_MODULE_0__.initializeEventHandlers)(parentWindow);\n    console.log('[WIO] World Info Optimizer initialized successfully.');\n}\n// --- 脚本启动 ---\n(() => {\n    // 使用IIFE封装，避免全局污染\n    console.log('[WIO Script] Execution started.');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/index.ts\n\n}");
  },
  "./src/world_info_optimizer/store.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/store.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collapseAllBooks: () => (/* binding */ collapseAllBooks),\n/* harmony export */   expandAllBooks: () => (/* binding */ expandAllBooks),\n/* harmony export */   getState: () => (/* binding */ getState),\n/* harmony export */   resetState: () => (/* binding */ resetState),\n/* harmony export */   setActiveTab: () => (/* binding */ setActiveTab),\n/* harmony export */   setAllData: () => (/* binding */ setAllData),\n/* harmony export */   setDataLoaded: () => (/* binding */ setDataLoaded),\n/* harmony export */   setLoadError: () => (/* binding */ setLoadError),\n/* harmony export */   setLoading: () => (/* binding */ setLoading),\n/* harmony export */   setSearchQuery: () => (/* binding */ setSearchQuery),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   toggleBookCollapse: () => (/* binding */ toggleBookCollapse),\n/* harmony export */   toggleItemSelection: () => (/* binding */ toggleItemSelection),\n/* harmony export */   toggleMultiSelectMode: () => (/* binding */ toggleMultiSelectMode),\n/* harmony export */   updateState: () => (/* binding */ updateState)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n// src/world_info_optimizer/store.ts\n\n// --- State Definition ---\nconst initialState = {\n    regexes: { global: [], character: [] },\n    lorebooks: { character: [] },\n    chatLorebook: null,\n    allLorebooks: [],\n    lorebookEntries: new Map(),\n    lorebookUsage: new Map(),\n    activeTab: _constants__WEBPACK_IMPORTED_MODULE_0__.INITIAL_ACTIVE_TAB,\n    isDataLoaded: false,\n    isLoading: false,\n    loadError: null,\n    searchFilters: {\n        bookName: true,\n        entryName: true,\n        keywords: true,\n        content: true,\n    },\n    searchQuery: '',\n    multiSelectMode: false,\n    selectedItems: new Set(),\n    collapsedBooks: new Set(),\n};\n// 使用深拷贝来创建可变状态，避免直接修改initialState\nlet state = JSON.parse(JSON.stringify(initialState));\n// Map和Set不能通过JSON.stringify/parse正确克隆，需要手动重新创建\nstate.lorebookEntries = new Map();\nstate.lorebookUsage = new Map();\nstate.selectedItems = new Set();\nstate.collapsedBooks = new Set();\nstate.isLoading = false;\nstate.loadError = null;\nconst listeners = [];\n/**\n * 订阅状态变化。\n * @param listener 当状态更新时要调用的回调函数。\n * @returns 一个取消订阅的函数。\n */\nconst subscribe = (listener) => {\n    listeners.push(listener);\n    return () => {\n        const index = listeners.indexOf(listener);\n        if (index > -1) {\n            listeners.splice(index, 1);\n        }\n    };\n};\n/**\n * 通知所有监听器状态已更新。\n */\nconst notify = () => {\n    // 传递状态的深拷贝，以防监听器意外修改状态\n    const deepCopiedState = {\n        ...state,\n        // 深拷贝 Map，包括其内部的数组\n        lorebookEntries: new Map(Array.from(state.lorebookEntries.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        // 深拷贝 Map，包括其内部的数组\n        lorebookUsage: new Map(Array.from(state.lorebookUsage.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        selectedItems: new Set(state.selectedItems),\n        collapsedBooks: new Set(state.collapsedBooks),\n        // 深拷贝嵌套对象\n        regexes: {\n            global: [...state.regexes.global],\n            character: [...state.regexes.character],\n        },\n        lorebooks: {\n            character: [...state.lorebooks.character],\n        },\n        allLorebooks: [...state.allLorebooks],\n        searchFilters: { ...state.searchFilters },\n    };\n    listeners.forEach(listener => listener(deepCopiedState));\n};\n// --- State Accessors and Mutators ---\n/**\n * 获取当前状态对象的快照。\n * @returns 当前应用状态的深拷贝。\n */\nconst getState = () => {\n    // 返回深拷贝以保证状态的不可变性\n    return {\n        ...state,\n        // 深拷贝 Map，包括其内部的数组\n        lorebookEntries: new Map(Array.from(state.lorebookEntries.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        // 深拷贝 Map，包括其内部的数组\n        lorebookUsage: new Map(Array.from(state.lorebookUsage.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        selectedItems: new Set(state.selectedItems),\n        collapsedBooks: new Set(state.collapsedBooks),\n        // 深拷贝嵌套对象\n        regexes: {\n            global: [...state.regexes.global],\n            character: [...state.regexes.character],\n        },\n        lorebooks: {\n            character: [...state.lorebooks.character],\n        },\n        allLorebooks: [...state.allLorebooks],\n        searchFilters: { ...state.searchFilters },\n    };\n};\n/**\n * 更新应用状态并通知所有订阅者。\n * @param updater 一个函数，接收当前状态并返回一个新的（或修改过的）状态部分。\n */\nconst updateState = (updater) => {\n    const updates = updater(state);\n    state = { ...state, ...updates };\n    notify();\n};\n/**\n * 重置整个应用状态到初始值。\n */\nconst resetState = () => {\n    state = JSON.parse(JSON.stringify(initialState));\n    state.lorebookEntries = new Map();\n    state.lorebookUsage = new Map();\n    state.selectedItems = new Set();\n    state.collapsedBooks = new Set();\n    notify();\n};\n// --- Specific State Updater Functions ---\nconst setActiveTab = (tabId) => {\n    updateState(s => ({ ...s, activeTab: tabId }));\n};\nconst setDataLoaded = (isLoaded) => {\n    updateState(s => ({ ...s, isDataLoaded: isLoaded }));\n};\nconst setLoading = (isLoading) => {\n    updateState(s => ({ ...s, isLoading }));\n};\nconst setLoadError = (error) => {\n    updateState(s => ({ ...s, loadError: error }));\n};\nconst setSearchQuery = (query) => {\n    updateState(s => ({ ...s, searchQuery: query }));\n};\nconst setAllData = (data) => {\n    updateState(s => ({\n        ...s,\n        regexes: { global: data.globalRegexes, character: data.characterRegexes },\n        allLorebooks: data.allLorebooks,\n        lorebooks: { character: data.characterLorebooks },\n        chatLorebook: data.chatLorebook,\n        lorebookEntries: data.lorebookEntries,\n        lorebookUsage: data.lorebookUsage,\n        isDataLoaded: true,\n    }));\n};\n// --- Multi-Select and Collapse State Management ---\nconst toggleMultiSelectMode = () => {\n    updateState(s => ({\n        ...s,\n        multiSelectMode: !s.multiSelectMode,\n        selectedItems: new Set(), // 清空选中项\n    }));\n};\nconst toggleItemSelection = (itemId) => {\n    updateState(s => {\n        const newSelectedItems = new Set(s.selectedItems);\n        if (newSelectedItems.has(itemId)) {\n            newSelectedItems.delete(itemId);\n        }\n        else {\n            newSelectedItems.add(itemId);\n        }\n        return { ...s, selectedItems: newSelectedItems };\n    });\n};\nconst toggleBookCollapse = (bookName) => {\n    updateState(s => {\n        const newCollapsedBooks = new Set(s.collapsedBooks);\n        if (newCollapsedBooks.has(bookName)) {\n            newCollapsedBooks.delete(bookName);\n        }\n        else {\n            newCollapsedBooks.add(bookName);\n        }\n        return { ...s, collapsedBooks: newCollapsedBooks };\n    });\n};\nconst collapseAllBooks = () => {\n    const state = getState();\n    const allBookNames = new Set();\n    // 收集所有世界书名称\n    state.allLorebooks.forEach(book => allBookNames.add(book.name));\n    state.lorebooks.character.forEach(bookName => allBookNames.add(bookName));\n    if (state.chatLorebook)\n        allBookNames.add(state.chatLorebook);\n    updateState(s => ({ ...s, collapsedBooks: allBookNames }));\n};\nconst expandAllBooks = () => {\n    updateState(s => ({ ...s, collapsedBooks: new Set() }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/store.ts\n\n}");
  },
  "./src/world_info_optimizer/ui.ts": 
  /*!****************************************!*\
  !*** ./src/world_info_optimizer/ui.ts ***!
  \****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initUI: () => (/* binding */ initUI),\n/* harmony export */   injectUI: () => (/* binding */ injectUI)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ "./src/world_info_optimizer/constants.ts");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store */ "./src/world_info_optimizer/store.ts");\n/* harmony import */ var _ui_views__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/views */ "./src/world_info_optimizer/ui/views.ts");\n// src/world_info_optimizer/ui.ts\n\n\n\n// --- Private Variables ---\nlet parentDoc;\nlet $;\n// --- Main Panel and Button Injection ---\nconst injectUI = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    if ($(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, parentDoc).length > 0) {\n        console.log(\'[WIO] UI already injected.\');\n        return;\n    }\n    const styles = `\n        :root {\n            /* 紧凑布局 */\n            --wio-font-size-sm: 11px;\n            --wio-font-size-md: 13px;\n            --wio-font-size-lg: 15px;\n            --wio-spacing-xs: 2px;\n            --wio-spacing-sm: 6px;\n            --wio-spacing-md: 10px;\n            --wio-spacing-lg: 14px;\n\n            /* 扁平化风格 */\n            --wio-border-radius: 4px;\n            --wio-shadow: none;\n            \n            /* 暗色主题 */\n            --wio-bg-primary: #1f1f1f;\n            --wio-bg-secondary: #2d2d2d;\n            --wio-bg-tertiary: #3c3c3c;\n            --wio-bg-toolbar: #252525;\n            --wio-text-primary: #e0e0e0;\n            --wio-text-secondary: #9e9e9e;\n            --wio-highlight-color: #29b6f6;\n            --wio-border-color: #424242;\n        }\n\n        /* All WIO styles here... */\n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} {\n            display: none;\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100vw;\n            height: 100vh;\n            z-index: 10000;\n            overflow: hidden;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} .wio-panel-inner {\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            width: 100vw;\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n        }\n\n\n        /* 头部样式 */\n        .wio-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        .wio-header h2 {\n            margin: 0;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-close-btn {\n            background: none;\n            border: none;\n            color: var(--wio-text-secondary);\n            font-size: 24px;\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: color 0.2s ease;\n        }\n        \n        .wio-close-btn:hover {\n            color: var(--wio-text-primary);\n        }\n        \n        /* 选项卡样式 */\n        .wio-tabs {\n            display: flex;\n            background-color: var(--wio-bg-tertiary);\n            overflow-x: auto;\n            white-space: nowrap;\n            flex-shrink: 0;\n            border-bottom: 1px solid var(--wio-border-color);\n            -ms-overflow-style: none;\n            scrollbar-width: none;\n        }\n        \n        .wio-tabs::-webkit-scrollbar {\n            display: none;\n        }\n        \n        .wio-tab-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            border-bottom: 2px solid transparent;\n            flex-shrink: 0;\n            font-size: var(--wio-font-size-md);\n            transition: all 0.2s ease;\n            outline: none;\n        }\n        \n        .wio-tab-btn.active {\n            color: var(--wio-highlight-color);\n            border-bottom-color: var(--wio-highlight-color);\n            background-color: rgba(41, 182, 246, 0.15);\n        }\n        \n        .wio-tab-btn:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n        \n        /* 工具栏样式 */\n        .wio-toolbar {\n            padding: var(--wio-spacing-md);\n            display: flex;\n            gap: var(--wio-spacing-md);\n            background-color: var(--wio-bg-toolbar);\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID} {\n            flex-grow: 1;\n            padding: var(--wio-spacing-md);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary); /* Darker background */\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-md);\n            outline: none;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}:focus {\n            border-color: var(--wio-highlight-color);\n            box-shadow: none;\n        }\n        \n        .wio-toolbar button {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: var(--wio-spacing-xs);\n            transition: all 0.2s ease;\n            outline: none;\n            min-width: 40px;\n            min-height: 40px;\n        }\n        \n        .wio-toolbar button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-toolbar button:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 主内容区域样式 */\n        .wio-main-content {\n            flex-grow: 1;\n            overflow-y: auto;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        /* 列表样式 */\n        .wio-book-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-book-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n        }\n        \n        .wio-book-header h4 {\n            margin: 0;\n            flex-grow: 1;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-usage-pill {\n            padding: 2px 8px;\n            background-color: var(--wio-highlight-color);\n            color: #1f1f1f;\n            border-radius: 12px;\n            font-size: var(--wio-font-size-sm);\n            font-weight: bold;\n        }\n        \n        .wio-item-controls {\n            display: flex;\n            gap: var(--wio-spacing-xs);\n        }\n        \n        .wio-item-controls button {\n            padding: var(--wio-spacing-xs);\n            background-color: transparent;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: all 0.2s ease;\n        }\n        \n        .wio-item-controls button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-entry-list {\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-entry-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-entry-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-entry-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n        }\n        \n        .wio-entry-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-entry-keys {\n            font-size: var(--wio-font-size-sm);\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            flex-grow: 1;\n            word-break: break-word;\n        }\n        \n        .wio-entry-actions,\n        .wio-regex-actions {\n            padding: var(--wio-spacing-md);\n            text-align: center;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-create-entry-btn,\n        .wio-create-regex-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-highlight-color);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-create-entry-btn:hover,\n        .wio-create-regex-btn:hover {\n            background-color: #0091ea; /* A slightly darker shade of the highlight color */\n        }\n\n        /* 正则表达式样式 */\n        .wio-regex-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-regex-group h3 {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            margin: 0;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-regex-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-regex-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-regex-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n            flex-wrap: wrap;\n        }\n        \n        .wio-regex-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-regex-find,\n        .wio-regex-replace {\n            background-color: var(--wio-bg-primary);\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: var(--wio-font-size-sm);\n            word-break: break-all;\n            border: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-info-text {\n            text-align: center;\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-container {\n            text-align: center;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-text {\n            color: #ff6b6b;\n            margin-bottom: var(--wio-spacing-md);\n            font-weight: bold;\n        }\n\n        .wio-retry-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: #c62828;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-retry-btn:hover {\n            background-color: #b71c1c;\n        }\n        \n        .wio-search-highlight {\n            background-color: rgba(255, 255, 0, 0.3);\n            padding: 0 2px;\n            border-radius: 2px;\n        }\n\n        /* 页脚样式 */\n        .wio-footer {\n            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            font-size: var(--wio-font-size-sm);\n            text-align: right;\n            border-top: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n\n        /* SweetAlert2 输入框样式覆盖 */\n        .swal2-input, .swal2-textarea {\n            background-color: var(--wio-bg-secondary) !important;\n            color: var(--wio-text-primary) !important;\n            border: 1px solid var(--wio-border-color) !important;\n        }\n        \n        .swal2-input:focus, .swal2-textarea:focus {\n            border-color: var(--wio-highlight-color) !important;\n            box-shadow: none !important;\n        }\n\n        /* SweetAlert2 模态框样式覆盖 */\n        .swal2-popup {\n            background-color: var(--wio-bg-primary) !important;\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-title {\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-html-container {\n            color: var(--wio-text-secondary) !important;\n        }\n\n        .swal2-confirm, .swal2-cancel {\n            border-radius: var(--wio-border-radius) !important;\n            transition: background-color 0.2s ease;\n        }\n\n        .swal2-confirm {\n            background-color: var(--wio-highlight-color) !important;\n        }\n        \n        .swal2-cancel {\n            background-color: var(--wio-bg-tertiary) !important;\n        }\n        \n        .swal2-toast {\n             background-color: var(--wio-bg-secondary) !important;\n             color: var(--wio-text-primary) !important;\n        }\n\n        /* 复选框样式优化 */\n        input[type="checkbox"] {\n            transform: scale(1.1);\n            accent-color: var(--wio-highlight-color);\n            margin-right: var(--wio-spacing-sm);\n            background-color: var(--wio-bg-secondary);\n            border: 1px solid var(--wio-border-color);\n            border-radius: 2px;\n            appearance: none;\n            -webkit-appearance: none;\n            width: 16px;\n            height: 16px;\n            cursor: pointer;\n            position: relative;\n            top: 2px;\n        }\n\n        input[type="checkbox"]:checked {\n            background-color: var(--wio-highlight-color);\n            border-color: var(--wio-highlight-color);\n        }\n\n        input[type="checkbox"]:checked::after {\n            content: \'✔\';\n            position: absolute;\n            color: #1f1f1f;\n            font-size: 12px;\n            top: -1px;\n            left: 2px;\n        }\n\n        /* 折叠按钮样式 */\n        .wio-collapse-toggle {\n            background: none;\n            border: none;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            margin-right: var(--wio-spacing-sm);\n            font-size: 14px;\n            transition: color 0.2s ease;\n            min-width: 20px;\n            text-align: center;\n        }\n\n        .wio-collapse-toggle:hover {\n            color: var(--wio-text-primary);\n        }\n\n        /* 多选复选框样式 */\n        .wio-multi-select-checkbox {\n            margin-right: var(--wio-spacing-sm);\n            border: 2px solid var(--wio-highlight-color) !important;\n        }\n\n        .wio-multi-select-checkbox:checked {\n            background-color: var(--wio-highlight-color) !important;\n        }\n\n        /* 工具栏按钮激活状态 */\n        .wio-toolbar button.active {\n            background-color: var(--wio-highlight-color);\n            color: #ffffff;\n        }\n\n        /* 批量操作输入框样式 */\n        #wio-bulk-search-input,\n        #wio-bulk-replace-input {\n            flex-grow: 1;\n            max-width: 200px;\n            padding: var(--wio-spacing-sm);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary);\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-sm);\n            outline: none;\n            transition: border-color 0.2s ease;\n        }\n\n        #wio-bulk-search-input:focus,\n        #wio-bulk-replace-input:focus {\n            border-color: var(--wio-highlight-color);\n        }\n\n        #wio-bulk-search-input::placeholder,\n        #wio-bulk-replace-input::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        /* 聚焦样式优化 */\n        :focus-visible {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 滚动条样式 */\n        .wio-main-content::-webkit-scrollbar,\n        .wio-tabs::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-track,\n        .wio-tabs::-webkit-scrollbar-track {\n            background: var(--wio-bg-tertiary);\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb,\n        .wio-tabs::-webkit-scrollbar-thumb {\n            background: var(--wio-border-color);\n            border-radius: 4px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb:hover,\n        .wio-tabs::-webkit-scrollbar-thumb:hover {\n            background: #777;\n        }\n\n        /* 媒体查询：小型设备优化 */\n        @media (max-width: 767px) {\n            :root {\n                --wio-spacing-xs: 2px;\n                --wio-spacing-sm: 6px;\n                --wio-spacing-md: 8px;\n                --wio-spacing-lg: 10px;\n                --wio-font-size-sm: 11px;\n                --wio-font-size-md: 13px;\n                --wio-font-size-lg: 15px;\n            }\n            \n            .wio-header h2 {\n                font-size: var(--wio-font-size-md);\n            }\n            \n            .wio-entry-main {\n                flex-wrap: wrap;\n            }\n            \n            .wio-entry-name {\n                flex-basis: 100%;\n                margin-bottom: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-main {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-name,\n            .wio-regex-find,\n            .wio-regex-replace {\n                width: 100%;\n                box-sizing: border-box;\n            }\n            \n            .wio-toolbar {\n                flex-wrap: wrap;\n                gap: var(--wio-spacing-sm); /* 缩小gap */\n            }\n            \n            #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID} {\n                flex-basis: 100%; /* 确保搜索框始终占满一行 */\n            }\n            \n            .wio-toolbar button {\n                flex-grow: 1; /* 让按钮平均分配剩余空间 */\n                min-width: 44px; /* 保证最小触摸尺寸 */\n            }\n\n            .wio-tabs {\n                position: relative; /* 为伪元素定位 */\n            }\n\n            .wio-tabs::before,\n            .wio-tabs::after {\n                content: \'\';\n                position: absolute;\n                top: 0;\n                bottom: 0;\n                width: 20px; /* 渐变宽度 */\n                pointer-events: none; /* 允许点击穿透 */\n            }\n\n            .wio-tabs::before {\n                left: 0;\n                background: linear-gradient(to right, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-tabs::after {\n                right: 0;\n                background: linear-gradient(to left, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-main-content {\n                padding: var(--wio-spacing-md); /* 统一内边距 */\n            }\n\n            .wio-entry-keys {\n                font-size: var(--wio-font-size-sm);\n                white-space: normal; /* 允许长文本换行 */\n                word-break: break-all;\n            }\n            \n            /* 触摸目标优化 */\n            button,\n            input[type="checkbox"] {\n                touch-action: manipulation;\n            }\n        }\n        \n        /* 平板设备优化 */\n        @media (min-width: 768px) and (max-width: 1024px) {\n            #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} {\n                width: 90%;\n                height: 80%;\n            }\n        }\n\n        /* 高对比度模式支持 */\n        @media (prefers-contrast: high) {\n            :root {\n                --wio-border-color: #fff !important;\n                --wio-bg-primary: #000 !important;\n                --wio-bg-secondary: #333 !important;\n                --wio-bg-tertiary: #222 !important;\n                --wio-bg-toolbar: #444 !important;\n                --wio-text-primary: #fff !important;\n                --wio-text-secondary: #ddd !important;\n                --wio-highlight-color: #ff0 !important;\n            }\n        }\n\n\n        /* 减少动画模式支持 */\n        @media (prefers-reduced-motion: reduce) {\n            * {\n                animation-duration: 0.01ms !important;\n                animation-iteration-count: 1 !important;\n                transition-duration: 0.01ms !important;\n            }\n        }\n    `;\n    const styleSheet = parentDoc.createElement(\'style\');\n    styleSheet.innerText = styles;\n    parentDoc.head.appendChild(styleSheet);\n    const panelHtml = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}">\n            <div class="wio-panel-inner">\n                <div class="wio-header">\n                    <h2>世界书 & 正则便捷管理 (WIO)</h2>\n                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>\n                </div>\n                <div class="wio-tabs" role="tablist">\n                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>\n                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>\n                </div>\n                <div class="wio-toolbar">\n                    <input type="search" id="${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}" placeholder="搜索..." aria-label="搜索内容">\n                    <input type="text" id="wio-bulk-search-input" placeholder="批量搜索..." aria-label="批量搜索内容" style="display: none;">\n                    <input type="text" id="wio-bulk-replace-input" placeholder="替换为..." aria-label="替换内容" style="display: none;">\n                    <button id="wio-bulk-replace-btn" title="批量替换" aria-label="批量替换" style="display: none;"><i class="fa-solid fa-exchange-alt"></i></button>\n                    <button id="wio-multi-select-btn" title="多选模式" aria-label="切换多选模式"><i class="fa-solid fa-check-square"></i></button>\n                    <button id="wio-bulk-delete-btn" title="批量删除" aria-label="批量删除选中项" style="display: none;"><i class="fa-solid fa-trash"></i></button>\n                    <button id="wio-bulk-enable-btn" title="批量启用" aria-label="批量启用选中项" style="display: none;"><i class="fa-solid fa-eye"></i></button>\n                    <button id="wio-bulk-disable-btn" title="批量禁用" aria-label="批量禁用选中项" style="display: none;"><i class="fa-solid fa-eye-slash"></i></button>\n                    <button id="wio-collapse-all-btn" title="全部折叠" aria-label="折叠所有世界书"><i class="fa-solid fa-compress"></i></button>\n                    <button id="wio-expand-all-btn" title="全部展开" aria-label="展开所有世界书"><i class="fa-solid fa-expand"></i></button>\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.CREATE_LOREBOOK_BTN_ID}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>\n                </div>\n                <div class="wio-main-content" role="main" id="tab-content-container"></div>\n                <div class="wio-footer">\n                    <span>WIO v3.0 (Refactored)</span>\n                </div>\n            </div>\n        </div>\n    `;\n    $(\'body\', parentDoc).append(panelHtml);\n    const extensionButton = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}" class="list-group-item">\n            <img src="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ICON_URL}" style="width: 24px; height: 24px; margin-right: 10px;">\n            <span>${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_TEXT_IN_MENU}</span>\n        </div>\n    `;\n    $(\'#extensionsMenu\', parentDoc).append(extensionButton);\n    console.log(\'[WIO] UI Injected successfully.\');\n};\n// --- Helper Functions ---\n/**\n * 更新工具栏按钮的显示状态\n */\nconst updateToolbarButtons = (state) => {\n    if (!parentDoc)\n        return;\n    const isLorebookTab = [\'global-lore\', \'char-lore\', \'chat-lore\'].includes(state.activeTab);\n    const hasSelectedItems = state.selectedItems.size > 0;\n    // 批量搜索替换相关按钮 - 只在世界书标签页显示\n    const $bulkSearchInput = $(`#wio-bulk-search-input`, parentDoc);\n    const $bulkReplaceInput = $(`#wio-bulk-replace-input`, parentDoc);\n    const $bulkReplaceBtn = $(`#wio-bulk-replace-btn`, parentDoc);\n    if (isLorebookTab && !state.multiSelectMode) {\n        $bulkSearchInput.show();\n        $bulkReplaceInput.show();\n        $bulkReplaceBtn.show();\n    }\n    else {\n        $bulkSearchInput.hide();\n        $bulkReplaceInput.hide();\n        $bulkReplaceBtn.hide();\n    }\n    // 多选模式按钮 - 只在世界书标签页显示\n    const $multiSelectBtn = $(`#wio-multi-select-btn`, parentDoc);\n    if (isLorebookTab) {\n        $multiSelectBtn.show();\n        $multiSelectBtn.toggleClass(\'active\', state.multiSelectMode);\n    }\n    else {\n        $multiSelectBtn.hide();\n    }\n    // 批量操作按钮 - 只在多选模式下显示\n    const $bulkDeleteBtn = $(`#wio-bulk-delete-btn`, parentDoc);\n    const $bulkEnableBtn = $(`#wio-bulk-enable-btn`, parentDoc);\n    const $bulkDisableBtn = $(`#wio-bulk-disable-btn`, parentDoc);\n    if (state.multiSelectMode && isLorebookTab) {\n        $bulkDeleteBtn.show();\n        $bulkEnableBtn.show();\n        $bulkDisableBtn.show();\n        // 根据是否有选中项来启用/禁用按钮\n        $bulkDeleteBtn.prop(\'disabled\', !hasSelectedItems);\n        $bulkEnableBtn.prop(\'disabled\', !hasSelectedItems);\n        $bulkDisableBtn.prop(\'disabled\', !hasSelectedItems);\n    }\n    else {\n        $bulkDeleteBtn.hide();\n        $bulkEnableBtn.hide();\n        $bulkDisableBtn.hide();\n    }\n    // 折叠/展开按钮 - 只在世界书标签页显示\n    const $collapseAllBtn = $(`#wio-collapse-all-btn`, parentDoc);\n    const $expandAllBtn = $(`#wio-expand-all-btn`, parentDoc);\n    if (isLorebookTab) {\n        $collapseAllBtn.show();\n        $expandAllBtn.show();\n    }\n    else {\n        $collapseAllBtn.hide();\n        $expandAllBtn.hide();\n    }\n};\n// --- Core Render Logic ---\nconst render = () => {\n    if (!parentDoc)\n        return;\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    if (!$panel.length)\n        return;\n    $panel.find(\'.wio-tab-btn\').removeClass(\'active\');\n    $panel.find(`.wio-tab-btn[data-tab-id="${state.activeTab}"]`).addClass(\'active\');\n    // 更新工具栏按钮状态\n    updateToolbarButtons(state);\n    const $mainContent = $panel.find(\'.wio-main-content\');\n    // 处理加载状态\n    if (state.isLoading) {\n        $mainContent.html(\'<p class="wio-info-text">正在加载数据...</p>\');\n        return;\n    }\n    // 处理错误状态\n    if (state.loadError) {\n        $mainContent.html(`\n      <div class="wio-error-container">\n        <p class="wio-error-text">${state.loadError}</p>\n        <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}-retry" class="wio-retry-btn">重试</button>\n      </div>\n    `);\n        return;\n    }\n    // 处理未加载状态\n    if (!state.isDataLoaded) {\n        $mainContent.html(\'<p class="wio-info-text">点击刷新按钮加载数据</p>\');\n        return;\n    }\n    const searchTerm = state.searchQuery.toLowerCase();\n    const $searchInput = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, parentDoc);\n    if ($searchInput.val() !== state.searchQuery) {\n        $searchInput.val(state.searchQuery);\n    }\n    switch (state.activeTab) {\n        case \'global-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderGlobalLorebookView)(state, searchTerm));\n            break;\n        case \'char-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderCharacterLorebookView)(state, searchTerm));\n            break;\n        case \'chat-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderChatLorebookView)(state, searchTerm));\n            break;\n        case \'global-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.global, searchTerm, \'全局正则\', \'global\'));\n            break;\n        case \'char-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.character, searchTerm, \'角色正则\', \'character\'));\n            break;\n        default:\n            $mainContent.html(`<p>未知视图: ${state.activeTab}</p>`);\n    }\n};\n// --- UI Initialization ---\nconst initUI = () => {\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.subscribe)(render);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvdWkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxpQ0FBaUM7QUFVWjtBQUN5QjtBQU0xQjtBQUVwQiw0QkFBNEI7QUFDNUIsSUFBSSxTQUFtQixDQUFDO0FBQ3hCLElBQUksQ0FBZSxDQUFDO0FBRXBCLDBDQUEwQztBQUVuQyxNQUFNLFFBQVEsR0FBRyxDQUFDLFlBQW9CLEVBQUUsRUFBRTtJQUMvQyxTQUFTLEdBQUcsWUFBWSxDQUFDLFFBQVEsQ0FBQztJQUNsQyxDQUFDLEdBQUcsWUFBWSxDQUFDLE1BQU0sQ0FBQztJQUV4QixJQUFJLENBQUMsQ0FBQyxJQUFJLGlEQUFTLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDN0MsT0FBTyxDQUFDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDO1FBQzFDLE9BQU87SUFDVCxDQUFDO0lBRUQsTUFBTSxNQUFNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztXQTJCTixnREFBUTs7Ozs7Ozs7Ozs7V0FXUixnREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1dBNkZSLHVEQUFlOzs7Ozs7Ozs7Ozs7V0FZZix1REFBZTs7OztXQUlmLHVEQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7ZUEyY1gsdURBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7ZUFvRGYsZ0RBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0tBNkJsQixDQUFDO0lBQ0osTUFBTSxVQUFVLEdBQUcsU0FBUyxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUNwRCxVQUFVLENBQUMsU0FBUyxHQUFHLE1BQU0sQ0FBQztJQUM5QixTQUFTLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUV2QyxNQUFNLFNBQVMsR0FBRzttQkFDRCxnREFBUTs7Ozs7Ozs7Ozs7Ozs7K0NBY29CLHVEQUFlOzs7Ozs7Ozs7O2tDQVU1QixzREFBYztrQ0FDZCw4REFBc0I7Ozs7Ozs7O0tBUW5ELENBQUM7SUFDSixDQUFDLENBQUMsTUFBTSxFQUFFLFNBQVMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUV2QyxNQUFNLGVBQWUsR0FBRzttQkFDUCxpREFBUzt3QkFDSix1REFBZTtvQkFDbkIsMkRBQW1COztLQUVsQyxDQUFDO0lBQ0osQ0FBQyxDQUFDLGlCQUFpQixFQUFFLFNBQVMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQztJQUV4RCxPQUFPLENBQUMsR0FBRyxDQUFDLGlDQUFpQyxDQUFDLENBQUM7QUFDakQsQ0FBQyxDQUFDO0FBRUYsMkJBQTJCO0FBRTNCOztHQUVHO0FBQ0gsTUFBTSxvQkFBb0IsR0FBRyxDQUFDLEtBQVUsRUFBRSxFQUFFO0lBQzFDLElBQUksQ0FBQyxTQUFTO1FBQUUsT0FBTztJQUV2QixNQUFNLGFBQWEsR0FBRyxDQUFDLGFBQWEsRUFBRSxXQUFXLEVBQUUsV0FBVyxDQUFDLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUMxRixNQUFNLGdCQUFnQixHQUFHLEtBQUssQ0FBQyxhQUFhLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQztJQUV0RCwwQkFBMEI7SUFDMUIsTUFBTSxnQkFBZ0IsR0FBRyxDQUFDLENBQUMsd0JBQXdCLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDaEUsTUFBTSxpQkFBaUIsR0FBRyxDQUFDLENBQUMseUJBQXlCLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDbEUsTUFBTSxlQUFlLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBRTlELElBQUksYUFBYSxJQUFJLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBQzVDLGdCQUFnQixDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3hCLGlCQUFpQixDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3pCLGVBQWUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUN6QixDQUFDO1NBQU0sQ0FBQztRQUNOLGdCQUFnQixDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3hCLGlCQUFpQixDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3pCLGVBQWUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUN6QixDQUFDO0lBRUQsc0JBQXNCO0lBQ3RCLE1BQU0sZUFBZSxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUM5RCxJQUFJLGFBQWEsRUFBRSxDQUFDO1FBQ2xCLGVBQWUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUN2QixlQUFlLENBQUMsV0FBVyxDQUFDLFFBQVEsRUFBRSxLQUFLLENBQUMsZUFBZSxDQUFDLENBQUM7SUFDL0QsQ0FBQztTQUFNLENBQUM7UUFDTixlQUFlLENBQUMsSUFBSSxFQUFFLENBQUM7SUFDekIsQ0FBQztJQUVELHFCQUFxQjtJQUNyQixNQUFNLGNBQWMsR0FBRyxDQUFDLENBQUMsc0JBQXNCLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDNUQsTUFBTSxjQUFjLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQzVELE1BQU0sZUFBZSxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUU5RCxJQUFJLEtBQUssQ0FBQyxlQUFlLElBQUksYUFBYSxFQUFFLENBQUM7UUFDM0MsY0FBYyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3RCLGNBQWMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUN0QixlQUFlLENBQUMsSUFBSSxFQUFFLENBQUM7UUFFdkIsbUJBQW1CO1FBQ25CLGNBQWMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztRQUNuRCxjQUFjLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDLGdCQUFnQixDQUFDLENBQUM7UUFDbkQsZUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQ3RELENBQUM7U0FBTSxDQUFDO1FBQ04sY0FBYyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3RCLGNBQWMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUN0QixlQUFlLENBQUMsSUFBSSxFQUFFLENBQUM7SUFDekIsQ0FBQztJQUVELHVCQUF1QjtJQUN2QixNQUFNLGVBQWUsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDOUQsTUFBTSxhQUFhLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBRTFELElBQUksYUFBYSxFQUFFLENBQUM7UUFDbEIsZUFBZSxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3ZCLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUN2QixDQUFDO1NBQU0sQ0FBQztRQUNOLGVBQWUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUN2QixhQUFhLENBQUMsSUFBSSxFQUFFLENBQUM7SUFDdkIsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGLDRCQUE0QjtBQUU1QixNQUFNLE1BQU0sR0FBRyxHQUFHLEVBQUU7SUFDbEIsSUFBSSxDQUFDLFNBQVM7UUFBRSxPQUFPO0lBRXZCLE1BQU0sS0FBSyxHQUFHLGdEQUFRLEVBQUUsQ0FBQztJQUN6QixNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxnREFBUSxFQUFFLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDNUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNO1FBQUUsT0FBTztJQUUzQixNQUFNLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUNsRCxNQUFNLENBQUMsSUFBSSxDQUFDLDZCQUE2QixLQUFLLENBQUMsU0FBUyxJQUFJLENBQUMsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUM7SUFFakYsWUFBWTtJQUNaLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxDQUFDO0lBRTVCLE1BQU0sWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQztJQUV0RCxTQUFTO0lBQ1QsSUFBSSxLQUFLLENBQUMsU0FBUyxFQUFFLENBQUM7UUFDcEIsWUFBWSxDQUFDLElBQUksQ0FBQyx3Q0FBd0MsQ0FBQyxDQUFDO1FBQzVELE9BQU87SUFDVCxDQUFDO0lBRUQsU0FBUztJQUNULElBQUksS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBQ3BCLFlBQVksQ0FBQyxJQUFJLENBQUM7O29DQUVjLEtBQUssQ0FBQyxTQUFTO3NCQUM3QixzREFBYzs7S0FFL0IsQ0FBQyxDQUFDO1FBQ0gsT0FBTztJQUNULENBQUM7SUFFRCxVQUFVO0lBQ1YsSUFBSSxDQUFDLEtBQUssQ0FBQyxZQUFZLEVBQUUsQ0FBQztRQUN4QixZQUFZLENBQUMsSUFBSSxDQUFDLHlDQUF5QyxDQUFDLENBQUM7UUFDN0QsT0FBTztJQUNULENBQUM7SUFFRCxNQUFNLFVBQVUsR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDLFdBQVcsRUFBRSxDQUFDO0lBQ25ELE1BQU0sWUFBWSxHQUFHLENBQUMsQ0FBQyxJQUFJLHVEQUFlLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUN6RCxJQUFJLFlBQVksQ0FBQyxHQUFHLEVBQUUsS0FBSyxLQUFLLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDN0MsWUFBWSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLENBQUM7SUFDdEMsQ0FBQztJQUVELFFBQVEsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBQ3hCLEtBQUssYUFBYTtZQUNoQixZQUFZLENBQUMsSUFBSSxDQUFDLG1FQUF3QixDQUFDLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBQy9ELE1BQU07UUFDUixLQUFLLFdBQVc7WUFDZCxZQUFZLENBQUMsSUFBSSxDQUFDLHNFQUEyQixDQUFDLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBQ2xFLE1BQU07UUFDUixLQUFLLFdBQVc7WUFDZCxZQUFZLENBQUMsSUFBSSxDQUFDLGlFQUFzQixDQUFDLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBQzdELE1BQU07UUFDUixLQUFLLGNBQWM7WUFDakIsWUFBWSxDQUFDLElBQUksQ0FBQywwREFBZSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLFVBQVUsRUFBRSxNQUFNLEVBQUUsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUN2RixNQUFNO1FBQ1IsS0FBSyxZQUFZO1lBQ2YsWUFBWSxDQUFDLElBQUksQ0FBQywwREFBZSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLFVBQVUsRUFBRSxNQUFNLEVBQUUsV0FBVyxDQUFDLENBQUMsQ0FBQztZQUM3RixNQUFNO1FBQ1I7WUFDRSxZQUFZLENBQUMsSUFBSSxDQUFDLFlBQVksS0FBSyxDQUFDLFNBQVMsTUFBTSxDQUFDLENBQUM7SUFDekQsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGLDRCQUE0QjtBQUVyQixNQUFNLE1BQU0sR0FBRyxHQUFHLEVBQUU7SUFDekIsaURBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztBQUNwQixDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXZlcm5faGVscGVyX3RlbXBsYXRlLy4vc3JjL3dvcmxkX2luZm9fb3B0aW1pemVyL3VpLnRzPyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvdWkudHNcblxuaW1wb3J0IHtcbiAgQlVUVE9OX0lDT05fVVJMLFxuICBCVVRUT05fSUQsXG4gIEJVVFRPTl9URVhUX0lOX01FTlUsXG4gIENSRUFURV9MT1JFQk9PS19CVE5fSUQsXG4gIFBBTkVMX0lELFxuICBSRUZSRVNIX0JUTl9JRCxcbiAgU0VBUkNIX0lOUFVUX0lELFxufSBmcm9tICcuL2NvbnN0YW50cyc7XG5pbXBvcnQgeyBnZXRTdGF0ZSwgc3Vic2NyaWJlIH0gZnJvbSAnLi9zdG9yZSc7XG5pbXBvcnQge1xuICByZW5kZXJDaGFyYWN0ZXJMb3JlYm9va1ZpZXcsXG4gIHJlbmRlckNoYXRMb3JlYm9va1ZpZXcsXG4gIHJlbmRlckdsb2JhbExvcmVib29rVmlldyxcbiAgcmVuZGVyUmVnZXhWaWV3LFxufSBmcm9tICcuL3VpL3ZpZXdzJztcblxuLy8gLS0tIFByaXZhdGUgVmFyaWFibGVzIC0tLVxubGV0IHBhcmVudERvYzogRG9jdW1lbnQ7XG5sZXQgJDogSlF1ZXJ5U3RhdGljO1xuXG4vLyAtLS0gTWFpbiBQYW5lbCBhbmQgQnV0dG9uIEluamVjdGlvbiAtLS1cblxuZXhwb3J0IGNvbnN0IGluamVjdFVJID0gKHBhcmVudFdpbmRvdzogV2luZG93KSA9PiB7XG4gIHBhcmVudERvYyA9IHBhcmVudFdpbmRvdy5kb2N1bWVudDtcbiAgJCA9IHBhcmVudFdpbmRvdy5qUXVlcnk7XG5cbiAgaWYgKCQoYCMke0JVVFRPTl9JRH1gLCBwYXJlbnREb2MpLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLmxvZygnW1dJT10gVUkgYWxyZWFkeSBpbmplY3RlZC4nKTtcbiAgICByZXR1cm47XG4gIH1cblxuICBjb25zdCBzdHlsZXMgPSBgXG4gICAgICAgIDpyb290IHtcbiAgICAgICAgICAgIC8qIOe0p+WHkeW4g+WxgCAqL1xuICAgICAgICAgICAgLS13aW8tZm9udC1zaXplLXNtOiAxMXB4O1xuICAgICAgICAgICAgLS13aW8tZm9udC1zaXplLW1kOiAxM3B4O1xuICAgICAgICAgICAgLS13aW8tZm9udC1zaXplLWxnOiAxNXB4O1xuICAgICAgICAgICAgLS13aW8tc3BhY2luZy14czogMnB4O1xuICAgICAgICAgICAgLS13aW8tc3BhY2luZy1zbTogNnB4O1xuICAgICAgICAgICAgLS13aW8tc3BhY2luZy1tZDogMTBweDtcbiAgICAgICAgICAgIC0td2lvLXNwYWNpbmctbGc6IDE0cHg7XG5cbiAgICAgICAgICAgIC8qIOaJgeW5s+WMlumjjuagvCAqL1xuICAgICAgICAgICAgLS13aW8tYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICAgICAgLS13aW8tc2hhZG93OiBub25lO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvKiDmmpfoibLkuLvpopggKi9cbiAgICAgICAgICAgIC0td2lvLWJnLXByaW1hcnk6ICMxZjFmMWY7XG4gICAgICAgICAgICAtLXdpby1iZy1zZWNvbmRhcnk6ICMyZDJkMmQ7XG4gICAgICAgICAgICAtLXdpby1iZy10ZXJ0aWFyeTogIzNjM2MzYztcbiAgICAgICAgICAgIC0td2lvLWJnLXRvb2xiYXI6ICMyNTI1MjU7XG4gICAgICAgICAgICAtLXdpby10ZXh0LXByaW1hcnk6ICNlMGUwZTA7XG4gICAgICAgICAgICAtLXdpby10ZXh0LXNlY29uZGFyeTogIzllOWU5ZTtcbiAgICAgICAgICAgIC0td2lvLWhpZ2hsaWdodC1jb2xvcjogIzI5YjZmNjtcbiAgICAgICAgICAgIC0td2lvLWJvcmRlci1jb2xvcjogIzQyNDI0MjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIEFsbCBXSU8gc3R5bGVzIGhlcmUuLi4gKi9cbiAgICAgICAgIyR7UEFORUxfSUR9IHtcbiAgICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XG4gICAgICAgICAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgICAgICAgICB0b3A6IDA7XG4gICAgICAgICAgICBsZWZ0OiAwO1xuICAgICAgICAgICAgd2lkdGg6IDEwMHZ3O1xuICAgICAgICAgICAgaGVpZ2h0OiAxMDB2aDtcbiAgICAgICAgICAgIHotaW5kZXg6IDEwMDAwO1xuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgIyR7UEFORUxfSUR9IC53aW8tcGFuZWwtaW5uZXIge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgICBoZWlnaHQ6IDEwMHZoO1xuICAgICAgICAgICAgd2lkdGg6IDEwMHZ3O1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXByaW1hcnkpO1xuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdpby10ZXh0LXByaW1hcnkpO1xuICAgICAgICB9XG5cblxuICAgICAgICAvKiDlpLTpg6jmoLflvI8gKi9cbiAgICAgICAgLndpby1oZWFkZXIge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpIHZhcigtLXdpby1zcGFjaW5nLWxnKTtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1iZy1zZWNvbmRhcnkpO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0td2lvLWJvcmRlci1jb2xvcik7XG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1oZWFkZXIgaDIge1xuICAgICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgICAgZm9udC1zaXplOiB2YXIoLS13aW8tZm9udC1zaXplLWxnKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1jbG9zZS1idG4ge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1zZWNvbmRhcnkpO1xuICAgICAgICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmcteHMpO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1jbG9zZS1idG46aG92ZXIge1xuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdpby10ZXh0LXByaW1hcnkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvKiDpgInpobnljaHmoLflvI8gKi9cbiAgICAgICAgLndpby10YWJzIHtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctdGVydGlhcnkpO1xuICAgICAgICAgICAgb3ZlcmZsb3cteDogYXV0bztcbiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS13aW8tYm9yZGVyLWNvbG9yKTtcbiAgICAgICAgICAgIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTtcbiAgICAgICAgICAgIHNjcm9sbGJhci13aWR0aDogbm9uZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby10YWJzOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXRhYi1idG4ge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpIHZhcigtLXdpby1zcGFjaW5nLWxnKTtcbiAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdpby10ZXh0LXNlY29uZGFyeSk7XG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1tZCk7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby10YWItYnRuLmFjdGl2ZSB7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLWhpZ2hsaWdodC1jb2xvcik7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tLWNvbG9yOiB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNDEsIDE4MiwgMjQ2LCAwLjE1KTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby10YWItYnRuOmZvY3VzIHtcbiAgICAgICAgICAgIG91dGxpbmU6IDJweCBzb2xpZCB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIG91dGxpbmUtb2Zmc2V0OiAycHg7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8qIOW3peWFt+agj+agt+W8jyAqL1xuICAgICAgICAud2lvLXRvb2xiYXIge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGdhcDogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXRvb2xiYXIpO1xuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXdpby1ib3JkZXItY29sb3IpO1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgICMke1NFQVJDSF9JTlBVVF9JRH0ge1xuICAgICAgICAgICAgZmxleC1ncm93OiAxO1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0td2lvLWJvcmRlci1jb2xvcik7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS13aW8tYm9yZGVyLXJhZGl1cyk7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctc2Vjb25kYXJ5KTsgLyogRGFya2VyIGJhY2tncm91bmQgKi9cbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1wcmltYXJ5KTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1tZCk7XG4gICAgICAgICAgICBvdXRsaW5lOiBub25lO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuMnMgZWFzZSwgYm94LXNoYWRvdyAwLjJzIGVhc2U7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgICMke1NFQVJDSF9JTlBVVF9JRH06OnBsYWNlaG9sZGVyIHtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1zZWNvbmRhcnkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAjJHtTRUFSQ0hfSU5QVVRfSUR9OmZvY3VzIHtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0td2lvLWhpZ2hsaWdodC1jb2xvcik7XG4gICAgICAgICAgICBib3gtc2hhZG93OiBub25lO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXRvb2xiYXIgYnV0dG9uIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKSB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS13aW8tYm9yZGVyLXJhZGl1cyk7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1zZWNvbmRhcnkpO1xuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgZm9udC1zaXplOiB2YXIoLS13aW8tZm9udC1zaXplLW1kKTtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgICBnYXA6IHZhcigtLXdpby1zcGFjaW5nLXhzKTtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gICAgICAgICAgICBvdXRsaW5lOiBub25lO1xuICAgICAgICAgICAgbWluLXdpZHRoOiA0MHB4O1xuICAgICAgICAgICAgbWluLWhlaWdodDogNDBweDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby10b29sYmFyIGJ1dHRvbjpob3ZlciB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctdGVydGlhcnkpO1xuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdpby10ZXh0LXByaW1hcnkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXRvb2xiYXIgYnV0dG9uOmZvY3VzIHtcbiAgICAgICAgICAgIG91dGxpbmU6IDJweCBzb2xpZCB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIG91dGxpbmUtb2Zmc2V0OiAycHg7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDkuLvlhoXlrrnljLrln5/moLflvI8gKi9cbiAgICAgICAgLndpby1tYWluLWNvbnRlbnQge1xuICAgICAgICAgICAgZmxleC1ncm93OiAxO1xuICAgICAgICAgICAgb3ZlcmZsb3cteTogYXV0bztcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLWxnKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLyog5YiX6KGo5qC35byPICovXG4gICAgICAgIC53aW8tYm9vay1ncm91cCB7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS13aW8tYm9yZGVyLXJhZGl1cyk7XG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXNlY29uZGFyeSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tYm9vay1oZWFkZXIge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpIHZhcigtLXdpby1zcGFjaW5nLWxnKTtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1iZy10ZXJ0aWFyeSk7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGdhcDogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLWJvb2staGVhZGVyIGg0IHtcbiAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgIGZsZXgtZ3JvdzogMTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1sZyk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tdXNhZ2UtcGlsbCB7XG4gICAgICAgICAgICBwYWRkaW5nOiAycHggOHB4O1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWhpZ2hsaWdodC1jb2xvcik7XG4gICAgICAgICAgICBjb2xvcjogIzFmMWYxZjtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICAgICAgICBmb250LXNpemU6IHZhcigtLXdpby1mb250LXNpemUtc20pO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8taXRlbS1jb250cm9scyB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgZ2FwOiB2YXIoLS13aW8tc3BhY2luZy14cyk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8taXRlbS1jb250cm9scyBidXR0b24ge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmcteHMpO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS13aW8tYm9yZGVyLXJhZGl1cyk7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtc2Vjb25kYXJ5KTtcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLWl0ZW0tY29udHJvbHMgYnV0dG9uOmhvdmVyIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1iZy10ZXJ0aWFyeSk7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtcHJpbWFyeSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tZW50cnktbGlzdCB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctc2Vjb25kYXJ5KTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1lbnRyeS1pdGVtIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKTtcbiAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS13aW8tYmctdGVydGlhcnkpO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tZW50cnktaXRlbTpsYXN0LWNoaWxkIHtcbiAgICAgICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tZW50cnktbWFpbiB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGdhcDogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICAgICAgZmxleC1ncm93OiAxO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLWVudHJ5LW5hbWUge1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1lbnRyeS1rZXlzIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1zbSk7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtc2Vjb25kYXJ5KTtcbiAgICAgICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgICAgICAgICAgIGZsZXgtZ3JvdzogMTtcbiAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLXdvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tZW50cnktYWN0aW9ucyxcbiAgICAgICAgLndpby1yZWdleC1hY3Rpb25zIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKTtcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1iZy10ZXJ0aWFyeSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tY3JlYXRlLWVudHJ5LWJ0bixcbiAgICAgICAgLndpby1jcmVhdGUtcmVnZXgtYnRuIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKSB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXdpby1ib3JkZXItcmFkaXVzKTtcbiAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmO1xuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgZm9udC1zaXplOiB2YXIoLS13aW8tZm9udC1zaXplLW1kKTtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlO1xuICAgICAgICB9XG5cbiAgICAgICAgLndpby1jcmVhdGUtZW50cnktYnRuOmhvdmVyLFxuICAgICAgICAud2lvLWNyZWF0ZS1yZWdleC1idG46aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwOTFlYTsgLyogQSBzbGlnaHRseSBkYXJrZXIgc2hhZGUgb2YgdGhlIGhpZ2hsaWdodCBjb2xvciAqL1xuICAgICAgICB9XG5cbiAgICAgICAgLyog5q2j5YiZ6KGo6L6+5byP5qC35byPICovXG4gICAgICAgIC53aW8tcmVnZXgtZ3JvdXAge1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogdmFyKC0td2lvLXNwYWNpbmctbGcpO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0td2lvLWJvcmRlci1yYWRpdXMpO1xuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1iZy1zZWNvbmRhcnkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXJlZ2V4LWdyb3VwIGgzIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKSB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctdGVydGlhcnkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXJlZ2V4LWl0ZW0ge1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmctbWQpO1xuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXdpby1iZy10ZXJ0aWFyeSk7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1yZWdleC1pdGVtOmxhc3QtY2hpbGQge1xuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1yZWdleC1tYWluIHtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgZ2FwOiB2YXIoLS13aW8tc3BhY2luZy1tZCk7XG4gICAgICAgICAgICBmbGV4LWdyb3c6IDE7XG4gICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tcmVnZXgtbmFtZSB7XG4gICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXJlZ2V4LWZpbmQsXG4gICAgICAgIC53aW8tcmVnZXgtcmVwbGFjZSB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctcHJpbWFyeSk7XG4gICAgICAgICAgICBwYWRkaW5nOiA0cHggOHB4O1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICAgICAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1zbSk7XG4gICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7XG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS13aW8tYm9yZGVyLWNvbG9yKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1pbmZvLXRleHQge1xuICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdpby10ZXh0LXNlY29uZGFyeSk7XG4gICAgICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XG4gICAgICAgICAgICBwYWRkaW5nOiB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgIH1cblxuICAgICAgICAud2lvLWVycm9yLWNvbnRhaW5lciB7XG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgICAgICBwYWRkaW5nOiB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgIH1cblxuICAgICAgICAud2lvLWVycm9yLXRleHQge1xuICAgICAgICAgICAgY29sb3I6ICNmZjZiNmI7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS13aW8tc3BhY2luZy1tZCk7XG4gICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgICAgfVxuXG4gICAgICAgIC53aW8tcmV0cnktYnRuIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLW1kKSB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjYzYyODI4O1xuICAgICAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0td2lvLWJvcmRlci1yYWRpdXMpO1xuICAgICAgICAgICAgY29sb3I6ICNmZmZmZmY7XG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBmb250LXNpemU6IHZhcigtLXdpby1mb250LXNpemUtbWQpO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XG4gICAgICAgIH1cblxuICAgICAgICAud2lvLXJldHJ5LWJ0bjpob3ZlciB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjYjcxYzFjO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAud2lvLXNlYXJjaC1oaWdobGlnaHQge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMCwgMC4zKTtcbiAgICAgICAgICAgIHBhZGRpbmc6IDAgMnB4O1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLyog6aG16ISa5qC35byPICovXG4gICAgICAgIC53aW8tZm9vdGVyIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLXNtKSB2YXIoLS13aW8tc3BhY2luZy1sZyk7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctdGVydGlhcnkpO1xuICAgICAgICAgICAgZm9udC1zaXplOiB2YXIoLS13aW8tZm9udC1zaXplLXNtKTtcbiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0O1xuICAgICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkIHZhcigtLXdpby1ib3JkZXItY29sb3IpO1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBTd2VldEFsZXJ0MiDovpPlhaXmoYbmoLflvI/opobnm5YgKi9cbiAgICAgICAgLnN3YWwyLWlucHV0LCAuc3dhbDItdGV4dGFyZWEge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXNlY29uZGFyeSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1wcmltYXJ5KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0td2lvLWJvcmRlci1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLnN3YWwyLWlucHV0OmZvY3VzLCAuc3dhbDItdGV4dGFyZWE6Zm9jdXMge1xuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLyogU3dlZXRBbGVydDIg5qih5oCB5qGG5qC35byP6KaG55uWICovXG4gICAgICAgIC5zd2FsMi1wb3B1cCB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctcHJpbWFyeSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1wcmltYXJ5KSAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLnN3YWwyLXRpdGxlIHtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1wcmltYXJ5KSAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLnN3YWwyLWh0bWwtY29udGFpbmVyIHtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1zZWNvbmRhcnkpICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cblxuICAgICAgICAuc3dhbDItY29uZmlybSwgLnN3YWwyLWNhbmNlbCB7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS13aW8tYm9yZGVyLXJhZGl1cykgIWltcG9ydGFudDtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlO1xuICAgICAgICB9XG5cbiAgICAgICAgLnN3YWwyLWNvbmZpcm0ge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWhpZ2hsaWdodC1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLnN3YWwyLWNhbmNlbCB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8tYmctdGVydGlhcnkpICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC5zd2FsMi10b2FzdCB7XG4gICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXNlY29uZGFyeSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtcHJpbWFyeSkgIWltcG9ydGFudDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIOWkjemAieahhuagt+W8j+S8mOWMliAqL1xuICAgICAgICBpbnB1dFt0eXBlPVwiY2hlY2tib3hcIl0ge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICAgICAgICAgICAgYWNjZW50LWNvbG9yOiB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogdmFyKC0td2lvLXNwYWNpbmctc20pO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXNlY29uZGFyeSk7XG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS13aW8tYm9yZGVyLWNvbG9yKTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgICAgICAgICAgIGFwcGVhcmFuY2U6IG5vbmU7XG4gICAgICAgICAgICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XG4gICAgICAgICAgICB3aWR0aDogMTZweDtcbiAgICAgICAgICAgIGhlaWdodDogMTZweDtcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgICAgICAgIHRvcDogMnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgaW5wdXRbdHlwZT1cImNoZWNrYm94XCJdOmNoZWNrZWQge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWhpZ2hsaWdodC1jb2xvcik7XG4gICAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXdpby1oaWdobGlnaHQtY29sb3IpO1xuICAgICAgICB9XG5cbiAgICAgICAgaW5wdXRbdHlwZT1cImNoZWNrYm94XCJdOmNoZWNrZWQ6OmFmdGVyIHtcbiAgICAgICAgICAgIGNvbnRlbnQ6ICfinJQnO1xuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgY29sb3I6ICMxZjFmMWY7XG4gICAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgICB0b3A6IC0xcHg7XG4gICAgICAgICAgICBsZWZ0OiAycHg7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDmipjlj6DmjInpkq7moLflvI8gKi9cbiAgICAgICAgLndpby1jb2xsYXBzZS10b2dnbGUge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS13aW8tdGV4dC1zZWNvbmRhcnkpO1xuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgcGFkZGluZzogdmFyKC0td2lvLXNwYWNpbmcteHMpO1xuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiB2YXIoLS13aW8tc3BhY2luZy1zbSk7XG4gICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjJzIGVhc2U7XG4gICAgICAgICAgICBtaW4td2lkdGg6IDIwcHg7XG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgIH1cblxuICAgICAgICAud2lvLWNvbGxhcHNlLXRvZ2dsZTpob3ZlciB7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtcHJpbWFyeSk7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDlpJrpgInlpI3pgInmoYbmoLflvI8gKi9cbiAgICAgICAgLndpby1tdWx0aS1zZWxlY3QtY2hlY2tib3gge1xuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiB2YXIoLS13aW8tc3BhY2luZy1zbSk7XG4gICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKSAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLndpby1tdWx0aS1zZWxlY3QtY2hlY2tib3g6Y2hlY2tlZCB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKSAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLyog5bel5YW35qCP5oyJ6ZKu5r+A5rS754q25oCBICovXG4gICAgICAgIC53aW8tdG9vbGJhciBidXR0b24uYWN0aXZlIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXdpby1oaWdobGlnaHQtY29sb3IpO1xuICAgICAgICAgICAgY29sb3I6ICNmZmZmZmY7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDmibnph4/mk43kvZzovpPlhaXmoYbmoLflvI8gKi9cbiAgICAgICAgI3dpby1idWxrLXNlYXJjaC1pbnB1dCxcbiAgICAgICAgI3dpby1idWxrLXJlcGxhY2UtaW5wdXQge1xuICAgICAgICAgICAgZmxleC1ncm93OiAxO1xuICAgICAgICAgICAgbWF4LXdpZHRoOiAyMDBweDtcbiAgICAgICAgICAgIHBhZGRpbmc6IHZhcigtLXdpby1zcGFjaW5nLXNtKTtcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLXdpby1ib3JkZXItY29sb3IpO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0td2lvLWJvcmRlci1yYWRpdXMpO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2lvLWJnLXNlY29uZGFyeSk7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtcHJpbWFyeSk7XG4gICAgICAgICAgICBmb250LXNpemU6IHZhcigtLXdpby1mb250LXNpemUtc20pO1xuICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjJzIGVhc2U7XG4gICAgICAgIH1cblxuICAgICAgICAjd2lvLWJ1bGstc2VhcmNoLWlucHV0OmZvY3VzLFxuICAgICAgICAjd2lvLWJ1bGstcmVwbGFjZS1pbnB1dDpmb2N1cyB7XG4gICAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXdpby1oaWdobGlnaHQtY29sb3IpO1xuICAgICAgICB9XG5cbiAgICAgICAgI3dpby1idWxrLXNlYXJjaC1pbnB1dDo6cGxhY2Vob2xkZXIsXG4gICAgICAgICN3aW8tYnVsay1yZXBsYWNlLWlucHV0OjpwbGFjZWhvbGRlciB7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0td2lvLXRleHQtc2Vjb25kYXJ5KTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLyog6IGa54Sm5qC35byP5LyY5YyWICovXG4gICAgICAgIDpmb2N1cy12aXNpYmxlIHtcbiAgICAgICAgICAgIG91dGxpbmU6IDJweCBzb2xpZCB2YXIoLS13aW8taGlnaGxpZ2h0LWNvbG9yKTtcbiAgICAgICAgICAgIG91dGxpbmUtb2Zmc2V0OiAycHg7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDmu5rliqjmnaHmoLflvI8gKi9cbiAgICAgICAgLndpby1tYWluLWNvbnRlbnQ6Oi13ZWJraXQtc2Nyb2xsYmFyLFxuICAgICAgICAud2lvLXRhYnM6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcbiAgICAgICAgICAgIHdpZHRoOiA4cHg7XG4gICAgICAgICAgICBoZWlnaHQ6IDhweDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1tYWluLWNvbnRlbnQ6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrLFxuICAgICAgICAud2lvLXRhYnM6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXdpby1iZy10ZXJ0aWFyeSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC53aW8tbWFpbi1jb250ZW50Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYixcbiAgICAgICAgLndpby10YWJzOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS13aW8tYm9yZGVyLWNvbG9yKTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLndpby1tYWluLWNvbnRlbnQ6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyLFxuICAgICAgICAud2lvLXRhYnM6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICM3Nzc7XG4gICAgICAgIH1cblxuICAgICAgICAvKiDlqpLkvZPmn6Xor6LvvJrlsI/lnovorr7lpIfkvJjljJYgKi9cbiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XG4gICAgICAgICAgICA6cm9vdCB7XG4gICAgICAgICAgICAgICAgLS13aW8tc3BhY2luZy14czogMnB4O1xuICAgICAgICAgICAgICAgIC0td2lvLXNwYWNpbmctc206IDZweDtcbiAgICAgICAgICAgICAgICAtLXdpby1zcGFjaW5nLW1kOiA4cHg7XG4gICAgICAgICAgICAgICAgLS13aW8tc3BhY2luZy1sZzogMTBweDtcbiAgICAgICAgICAgICAgICAtLXdpby1mb250LXNpemUtc206IDExcHg7XG4gICAgICAgICAgICAgICAgLS13aW8tZm9udC1zaXplLW1kOiAxM3B4O1xuICAgICAgICAgICAgICAgIC0td2lvLWZvbnQtc2l6ZS1sZzogMTVweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLndpby1oZWFkZXIgaDIge1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1tZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC53aW8tZW50cnktbWFpbiB7XG4gICAgICAgICAgICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICAud2lvLWVudHJ5LW5hbWUge1xuICAgICAgICAgICAgICAgIGZsZXgtYmFzaXM6IDEwMCU7XG4gICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogdmFyKC0td2lvLXNwYWNpbmcteHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICAud2lvLXJlZ2V4LW1haW4ge1xuICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICAgICAgICAgICAgZ2FwOiB2YXIoLS13aW8tc3BhY2luZy14cyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC53aW8tcmVnZXgtbmFtZSxcbiAgICAgICAgICAgIC53aW8tcmVnZXgtZmluZCxcbiAgICAgICAgICAgIC53aW8tcmVnZXgtcmVwbGFjZSB7XG4gICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLndpby10b29sYmFyIHtcbiAgICAgICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgICAgICAgICAgICAgZ2FwOiB2YXIoLS13aW8tc3BhY2luZy1zbSk7IC8qIOe8qeWwj2dhcCAqL1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICAjJHtTRUFSQ0hfSU5QVVRfSUR9IHtcbiAgICAgICAgICAgICAgICBmbGV4LWJhc2lzOiAxMDAlOyAvKiDnoa7kv53mkJzntKLmoYblp4vnu4jljaDmu6HkuIDooYwgKi9cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLndpby10b29sYmFyIGJ1dHRvbiB7XG4gICAgICAgICAgICAgICAgZmxleC1ncm93OiAxOyAvKiDorqnmjInpkq7lubPlnYfliIbphY3liankvZnnqbrpl7QgKi9cbiAgICAgICAgICAgICAgICBtaW4td2lkdGg6IDQ0cHg7IC8qIOS/neivgeacgOWwj+inpuaRuOWwuuWvuCAqL1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAud2lvLXRhYnMge1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsgLyog5Li65Lyq5YWD57Sg5a6a5L2NICovXG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC53aW8tdGFiczo6YmVmb3JlLFxuICAgICAgICAgICAgLndpby10YWJzOjphZnRlciB7XG4gICAgICAgICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICAgIHRvcDogMDtcbiAgICAgICAgICAgICAgICBib3R0b206IDA7XG4gICAgICAgICAgICAgICAgd2lkdGg6IDIwcHg7IC8qIOa4kOWPmOWuveW6piAqL1xuICAgICAgICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOyAvKiDlhYHorrjngrnlh7vnqb/pgI8gKi9cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLndpby10YWJzOjpiZWZvcmUge1xuICAgICAgICAgICAgICAgIGxlZnQ6IDA7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCB2YXIoLS13aW8tYmctdGVydGlhcnkpLCB0cmFuc3BhcmVudCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC53aW8tdGFiczo6YWZ0ZXIge1xuICAgICAgICAgICAgICAgIHJpZ2h0OiAwO1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byBsZWZ0LCB2YXIoLS13aW8tYmctdGVydGlhcnkpLCB0cmFuc3BhcmVudCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC53aW8tbWFpbi1jb250ZW50IHtcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiB2YXIoLS13aW8tc3BhY2luZy1tZCk7IC8qIOe7n+S4gOWGhei+uei3nSAqL1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAud2lvLWVudHJ5LWtleXMge1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogdmFyKC0td2lvLWZvbnQtc2l6ZS1zbSk7XG4gICAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsgLyog5YWB6K646ZW/5paH5pys5o2i6KGMICovXG4gICAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICAvKiDop6bmkbjnm67moIfkvJjljJYgKi9cbiAgICAgICAgICAgIGJ1dHRvbixcbiAgICAgICAgICAgIGlucHV0W3R5cGU9XCJjaGVja2JveFwiXSB7XG4gICAgICAgICAgICAgICAgdG91Y2gtYWN0aW9uOiBtYW5pcHVsYXRpb247XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8qIOW5s+adv+iuvuWkh+S8mOWMliAqL1xuICAgICAgICBAbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIGFuZCAobWF4LXdpZHRoOiAxMDI0cHgpIHtcbiAgICAgICAgICAgICMke1BBTkVMX0lEfSB7XG4gICAgICAgICAgICAgICAgd2lkdGg6IDkwJTtcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDgwJTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8qIOmrmOWvueavlOW6puaooeW8j+aUr+aMgSAqL1xuICAgICAgICBAbWVkaWEgKHByZWZlcnMtY29udHJhc3Q6IGhpZ2gpIHtcbiAgICAgICAgICAgIDpyb290IHtcbiAgICAgICAgICAgICAgICAtLXdpby1ib3JkZXItY29sb3I6ICNmZmYgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAtLXdpby1iZy1wcmltYXJ5OiAjMDAwICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgLS13aW8tYmctc2Vjb25kYXJ5OiAjMzMzICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgLS13aW8tYmctdGVydGlhcnk6ICMyMjIgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAtLXdpby1iZy10b29sYmFyOiAjNDQ0ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgLS13aW8tdGV4dC1wcmltYXJ5OiAjZmZmICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgLS13aW8tdGV4dC1zZWNvbmRhcnk6ICNkZGQgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAtLXdpby1oaWdobGlnaHQtY29sb3I6ICNmZjAgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG5cbiAgICAgICAgLyog5YeP5bCR5Yqo55S75qih5byP5pSv5oyBICovXG4gICAgICAgIEBtZWRpYSAocHJlZmVycy1yZWR1Y2VkLW1vdGlvbjogcmVkdWNlKSB7XG4gICAgICAgICAgICAqIHtcbiAgICAgICAgICAgICAgICBhbmltYXRpb24tZHVyYXRpb246IDAuMDFtcyAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgIGFuaW1hdGlvbi1pdGVyYXRpb24tY291bnQ6IDEgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAwLjAxbXMgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIGA7XG4gIGNvbnN0IHN0eWxlU2hlZXQgPSBwYXJlbnREb2MuY3JlYXRlRWxlbWVudCgnc3R5bGUnKTtcbiAgc3R5bGVTaGVldC5pbm5lclRleHQgPSBzdHlsZXM7XG4gIHBhcmVudERvYy5oZWFkLmFwcGVuZENoaWxkKHN0eWxlU2hlZXQpO1xuXG4gIGNvbnN0IHBhbmVsSHRtbCA9IGBcbiAgICAgICAgPGRpdiBpZD1cIiR7UEFORUxfSUR9XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLXBhbmVsLWlubmVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cIndpby1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGgyPuS4lueVjOS5piAmIOato+WImeS+v+aNt+euoeeQhiAoV0lPKTwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3aW8tY2xvc2UtYnRuXCIgYXJpYS1sYWJlbD1cIuWFs+mXremdouadv1wiPiZ0aW1lczs8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLXRhYnNcIiByb2xlPVwidGFibGlzdFwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzPVwid2lvLXRhYi1idG5cIiBkYXRhLXRhYi1pZD1cImdsb2JhbC1sb3JlXCIgcm9sZT1cInRhYlwiIGFyaWEtY29udHJvbHM9XCJnbG9iYWwtbG9yZS1jb250ZW50XCIgYXJpYS1zZWxlY3RlZD1cImZhbHNlXCI+5YWo5bGA5LiW55WM5LmmPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3aW8tdGFiLWJ0blwiIGRhdGEtdGFiLWlkPVwiY2hhci1sb3JlXCIgcm9sZT1cInRhYlwiIGFyaWEtY29udHJvbHM9XCJjaGFyLWxvcmUtY29udGVudFwiIGFyaWEtc2VsZWN0ZWQ9XCJmYWxzZVwiPuinkuiJsuS4lueVjOS5pjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzPVwid2lvLXRhYi1idG5cIiBkYXRhLXRhYi1pZD1cImNoYXQtbG9yZVwiIHJvbGU9XCJ0YWJcIiBhcmlhLWNvbnRyb2xzPVwiY2hhdC1sb3JlLWNvbnRlbnRcIiBhcmlhLXNlbGVjdGVkPVwiZmFsc2VcIj7ogYrlpKnkuJbnlYzkuaY8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzcz1cIndpby10YWItYnRuXCIgZGF0YS10YWItaWQ9XCJnbG9iYWwtcmVnZXhcIiByb2xlPVwidGFiXCIgYXJpYS1jb250cm9scz1cImdsb2JhbC1yZWdleC1jb250ZW50XCIgYXJpYS1zZWxlY3RlZD1cImZhbHNlXCI+5YWo5bGA5q2j5YiZPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3aW8tdGFiLWJ0blwiIGRhdGEtdGFiLWlkPVwiY2hhci1yZWdleFwiIHJvbGU9XCJ0YWJcIiBhcmlhLWNvbnRyb2xzPVwiY2hhci1yZWdleC1jb250ZW50XCIgYXJpYS1zZWxlY3RlZD1cImZhbHNlXCI+6KeS6Imy5q2j5YiZPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cIndpby10b29sYmFyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dCB0eXBlPVwic2VhcmNoXCIgaWQ9XCIke1NFQVJDSF9JTlBVVF9JRH1cIiBwbGFjZWhvbGRlcj1cIuaQnOe0oi4uLlwiIGFyaWEtbGFiZWw9XCLmkJzntKLlhoXlrrlcIj5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJ0ZXh0XCIgaWQ9XCJ3aW8tYnVsay1zZWFyY2gtaW5wdXRcIiBwbGFjZWhvbGRlcj1cIuaJuemHj+aQnOe0oi4uLlwiIGFyaWEtbGFiZWw9XCLmibnph4/mkJzntKLlhoXlrrlcIiBzdHlsZT1cImRpc3BsYXk6IG5vbmU7XCI+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dCB0eXBlPVwidGV4dFwiIGlkPVwid2lvLWJ1bGstcmVwbGFjZS1pbnB1dFwiIHBsYWNlaG9sZGVyPVwi5pu/5o2i5Li6Li4uXCIgYXJpYS1sYWJlbD1cIuabv+aNouWGheWuuVwiIHN0eWxlPVwiZGlzcGxheTogbm9uZTtcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIndpby1idWxrLXJlcGxhY2UtYnRuXCIgdGl0bGU9XCLmibnph4/mm7/mjaJcIiBhcmlhLWxhYmVsPVwi5om56YeP5pu/5o2iXCIgc3R5bGU9XCJkaXNwbGF5OiBub25lO1wiPjxpIGNsYXNzPVwiZmEtc29saWQgZmEtZXhjaGFuZ2UtYWx0XCI+PC9pPjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGlkPVwid2lvLW11bHRpLXNlbGVjdC1idG5cIiB0aXRsZT1cIuWkmumAieaooeW8j1wiIGFyaWEtbGFiZWw9XCLliIfmjaLlpJrpgInmqKHlvI9cIj48aSBjbGFzcz1cImZhLXNvbGlkIGZhLWNoZWNrLXNxdWFyZVwiPjwvaT48L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIndpby1idWxrLWRlbGV0ZS1idG5cIiB0aXRsZT1cIuaJuemHj+WIoOmZpFwiIGFyaWEtbGFiZWw9XCLmibnph4/liKDpmaTpgInkuK3poblcIiBzdHlsZT1cImRpc3BsYXk6IG5vbmU7XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS10cmFzaFwiPjwvaT48L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIndpby1idWxrLWVuYWJsZS1idG5cIiB0aXRsZT1cIuaJuemHj+WQr+eUqFwiIGFyaWEtbGFiZWw9XCLmibnph4/lkK/nlKjpgInkuK3poblcIiBzdHlsZT1cImRpc3BsYXk6IG5vbmU7XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS1leWVcIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gaWQ9XCJ3aW8tYnVsay1kaXNhYmxlLWJ0blwiIHRpdGxlPVwi5om56YeP56aB55SoXCIgYXJpYS1sYWJlbD1cIuaJuemHj+emgeeUqOmAieS4remhuVwiIHN0eWxlPVwiZGlzcGxheTogbm9uZTtcIj48aSBjbGFzcz1cImZhLXNvbGlkIGZhLWV5ZS1zbGFzaFwiPjwvaT48L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIndpby1jb2xsYXBzZS1hbGwtYnRuXCIgdGl0bGU9XCLlhajpg6jmipjlj6BcIiBhcmlhLWxhYmVsPVwi5oqY5Y+g5omA5pyJ5LiW55WM5LmmXCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS1jb21wcmVzc1wiPjwvaT48L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIndpby1leHBhbmQtYWxsLWJ0blwiIHRpdGxlPVwi5YWo6YOo5bGV5byAXCIgYXJpYS1sYWJlbD1cIuWxleW8gOaJgOacieS4lueVjOS5plwiPjxpIGNsYXNzPVwiZmEtc29saWQgZmEtZXhwYW5kXCI+PC9pPjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGlkPVwiJHtSRUZSRVNIX0JUTl9JRH1cIiB0aXRsZT1cIuWIt+aWsOaVsOaNrlwiIGFyaWEtbGFiZWw9XCLliLfmlrDmlbDmja5cIj48aSBjbGFzcz1cImZhLXNvbGlkIGZhLXN5bmNcIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gaWQ9XCIke0NSRUFURV9MT1JFQk9PS19CVE5fSUR9XCIgdGl0bGU9XCLmlrDlu7rkuJbnlYzkuaZcIiBhcmlhLWxhYmVsPVwi5paw5bu65LiW55WM5LmmXCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS1wbHVzXCI+PC9pPjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ3aW8tbWFpbi1jb250ZW50XCIgcm9sZT1cIm1haW5cIiBpZD1cInRhYi1jb250ZW50LWNvbnRhaW5lclwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ3aW8tZm9vdGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPldJTyB2My4wIChSZWZhY3RvcmVkKTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICBgO1xuICAkKCdib2R5JywgcGFyZW50RG9jKS5hcHBlbmQocGFuZWxIdG1sKTtcblxuICBjb25zdCBleHRlbnNpb25CdXR0b24gPSBgXG4gICAgICAgIDxkaXYgaWQ9XCIke0JVVFRPTl9JRH1cIiBjbGFzcz1cImxpc3QtZ3JvdXAtaXRlbVwiPlxuICAgICAgICAgICAgPGltZyBzcmM9XCIke0JVVFRPTl9JQ09OX1VSTH1cIiBzdHlsZT1cIndpZHRoOiAyNHB4OyBoZWlnaHQ6IDI0cHg7IG1hcmdpbi1yaWdodDogMTBweDtcIj5cbiAgICAgICAgICAgIDxzcGFuPiR7QlVUVE9OX1RFWFRfSU5fTUVOVX08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgIGA7XG4gICQoJyNleHRlbnNpb25zTWVudScsIHBhcmVudERvYykuYXBwZW5kKGV4dGVuc2lvbkJ1dHRvbik7XG5cbiAgY29uc29sZS5sb2coJ1tXSU9dIFVJIEluamVjdGVkIHN1Y2Nlc3NmdWxseS4nKTtcbn07XG5cbi8vIC0tLSBIZWxwZXIgRnVuY3Rpb25zIC0tLVxuXG4vKipcbiAqIOabtOaWsOW3peWFt+agj+aMiemSrueahOaYvuekuueKtuaAgVxuICovXG5jb25zdCB1cGRhdGVUb29sYmFyQnV0dG9ucyA9IChzdGF0ZTogYW55KSA9PiB7XG4gIGlmICghcGFyZW50RG9jKSByZXR1cm47XG5cbiAgY29uc3QgaXNMb3JlYm9va1RhYiA9IFsnZ2xvYmFsLWxvcmUnLCAnY2hhci1sb3JlJywgJ2NoYXQtbG9yZSddLmluY2x1ZGVzKHN0YXRlLmFjdGl2ZVRhYik7XG4gIGNvbnN0IGhhc1NlbGVjdGVkSXRlbXMgPSBzdGF0ZS5zZWxlY3RlZEl0ZW1zLnNpemUgPiAwO1xuXG4gIC8vIOaJuemHj+aQnOe0ouabv+aNouebuOWFs+aMiemSriAtIOWPquWcqOS4lueVjOS5puagh+etvumhteaYvuekulxuICBjb25zdCAkYnVsa1NlYXJjaElucHV0ID0gJChgI3dpby1idWxrLXNlYXJjaC1pbnB1dGAsIHBhcmVudERvYyk7XG4gIGNvbnN0ICRidWxrUmVwbGFjZUlucHV0ID0gJChgI3dpby1idWxrLXJlcGxhY2UtaW5wdXRgLCBwYXJlbnREb2MpO1xuICBjb25zdCAkYnVsa1JlcGxhY2VCdG4gPSAkKGAjd2lvLWJ1bGstcmVwbGFjZS1idG5gLCBwYXJlbnREb2MpO1xuXG4gIGlmIChpc0xvcmVib29rVGFiICYmICFzdGF0ZS5tdWx0aVNlbGVjdE1vZGUpIHtcbiAgICAkYnVsa1NlYXJjaElucHV0LnNob3coKTtcbiAgICAkYnVsa1JlcGxhY2VJbnB1dC5zaG93KCk7XG4gICAgJGJ1bGtSZXBsYWNlQnRuLnNob3coKTtcbiAgfSBlbHNlIHtcbiAgICAkYnVsa1NlYXJjaElucHV0LmhpZGUoKTtcbiAgICAkYnVsa1JlcGxhY2VJbnB1dC5oaWRlKCk7XG4gICAgJGJ1bGtSZXBsYWNlQnRuLmhpZGUoKTtcbiAgfVxuXG4gIC8vIOWkmumAieaooeW8j+aMiemSriAtIOWPquWcqOS4lueVjOS5puagh+etvumhteaYvuekulxuICBjb25zdCAkbXVsdGlTZWxlY3RCdG4gPSAkKGAjd2lvLW11bHRpLXNlbGVjdC1idG5gLCBwYXJlbnREb2MpO1xuICBpZiAoaXNMb3JlYm9va1RhYikge1xuICAgICRtdWx0aVNlbGVjdEJ0bi5zaG93KCk7XG4gICAgJG11bHRpU2VsZWN0QnRuLnRvZ2dsZUNsYXNzKCdhY3RpdmUnLCBzdGF0ZS5tdWx0aVNlbGVjdE1vZGUpO1xuICB9IGVsc2Uge1xuICAgICRtdWx0aVNlbGVjdEJ0bi5oaWRlKCk7XG4gIH1cblxuICAvLyDmibnph4/mk43kvZzmjInpkq4gLSDlj6rlnKjlpJrpgInmqKHlvI/kuIvmmL7npLpcbiAgY29uc3QgJGJ1bGtEZWxldGVCdG4gPSAkKGAjd2lvLWJ1bGstZGVsZXRlLWJ0bmAsIHBhcmVudERvYyk7XG4gIGNvbnN0ICRidWxrRW5hYmxlQnRuID0gJChgI3dpby1idWxrLWVuYWJsZS1idG5gLCBwYXJlbnREb2MpO1xuICBjb25zdCAkYnVsa0Rpc2FibGVCdG4gPSAkKGAjd2lvLWJ1bGstZGlzYWJsZS1idG5gLCBwYXJlbnREb2MpO1xuXG4gIGlmIChzdGF0ZS5tdWx0aVNlbGVjdE1vZGUgJiYgaXNMb3JlYm9va1RhYikge1xuICAgICRidWxrRGVsZXRlQnRuLnNob3coKTtcbiAgICAkYnVsa0VuYWJsZUJ0bi5zaG93KCk7XG4gICAgJGJ1bGtEaXNhYmxlQnRuLnNob3coKTtcblxuICAgIC8vIOagueaNruaYr+WQpuaciemAieS4remhueadpeWQr+eUqC/npoHnlKjmjInpkq5cbiAgICAkYnVsa0RlbGV0ZUJ0bi5wcm9wKCdkaXNhYmxlZCcsICFoYXNTZWxlY3RlZEl0ZW1zKTtcbiAgICAkYnVsa0VuYWJsZUJ0bi5wcm9wKCdkaXNhYmxlZCcsICFoYXNTZWxlY3RlZEl0ZW1zKTtcbiAgICAkYnVsa0Rpc2FibGVCdG4ucHJvcCgnZGlzYWJsZWQnLCAhaGFzU2VsZWN0ZWRJdGVtcyk7XG4gIH0gZWxzZSB7XG4gICAgJGJ1bGtEZWxldGVCdG4uaGlkZSgpO1xuICAgICRidWxrRW5hYmxlQnRuLmhpZGUoKTtcbiAgICAkYnVsa0Rpc2FibGVCdG4uaGlkZSgpO1xuICB9XG5cbiAgLy8g5oqY5Y+gL+WxleW8gOaMiemSriAtIOWPquWcqOS4lueVjOS5puagh+etvumhteaYvuekulxuICBjb25zdCAkY29sbGFwc2VBbGxCdG4gPSAkKGAjd2lvLWNvbGxhcHNlLWFsbC1idG5gLCBwYXJlbnREb2MpO1xuICBjb25zdCAkZXhwYW5kQWxsQnRuID0gJChgI3dpby1leHBhbmQtYWxsLWJ0bmAsIHBhcmVudERvYyk7XG5cbiAgaWYgKGlzTG9yZWJvb2tUYWIpIHtcbiAgICAkY29sbGFwc2VBbGxCdG4uc2hvdygpO1xuICAgICRleHBhbmRBbGxCdG4uc2hvdygpO1xuICB9IGVsc2Uge1xuICAgICRjb2xsYXBzZUFsbEJ0bi5oaWRlKCk7XG4gICAgJGV4cGFuZEFsbEJ0bi5oaWRlKCk7XG4gIH1cbn07XG5cbi8vIC0tLSBDb3JlIFJlbmRlciBMb2dpYyAtLS1cblxuY29uc3QgcmVuZGVyID0gKCkgPT4ge1xuICBpZiAoIXBhcmVudERvYykgcmV0dXJuO1xuXG4gIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgY29uc3QgJHBhbmVsID0gJChgIyR7UEFORUxfSUR9YCwgcGFyZW50RG9jKTtcbiAgaWYgKCEkcGFuZWwubGVuZ3RoKSByZXR1cm47XG5cbiAgJHBhbmVsLmZpbmQoJy53aW8tdGFiLWJ0bicpLnJlbW92ZUNsYXNzKCdhY3RpdmUnKTtcbiAgJHBhbmVsLmZpbmQoYC53aW8tdGFiLWJ0bltkYXRhLXRhYi1pZD1cIiR7c3RhdGUuYWN0aXZlVGFifVwiXWApLmFkZENsYXNzKCdhY3RpdmUnKTtcblxuICAvLyDmm7TmlrDlt6XlhbfmoI/mjInpkq7nirbmgIFcbiAgdXBkYXRlVG9vbGJhckJ1dHRvbnMoc3RhdGUpO1xuXG4gIGNvbnN0ICRtYWluQ29udGVudCA9ICRwYW5lbC5maW5kKCcud2lvLW1haW4tY29udGVudCcpO1xuXG4gIC8vIOWkhOeQhuWKoOi9veeKtuaAgVxuICBpZiAoc3RhdGUuaXNMb2FkaW5nKSB7XG4gICAgJG1haW5Db250ZW50Lmh0bWwoJzxwIGNsYXNzPVwid2lvLWluZm8tdGV4dFwiPuato+WcqOWKoOi9veaVsOaNri4uLjwvcD4nKTtcbiAgICByZXR1cm47XG4gIH1cblxuICAvLyDlpITnkIbplJnor6/nirbmgIFcbiAgaWYgKHN0YXRlLmxvYWRFcnJvcikge1xuICAgICRtYWluQ29udGVudC5odG1sKGBcbiAgICAgIDxkaXYgY2xhc3M9XCJ3aW8tZXJyb3ItY29udGFpbmVyXCI+XG4gICAgICAgIDxwIGNsYXNzPVwid2lvLWVycm9yLXRleHRcIj4ke3N0YXRlLmxvYWRFcnJvcn08L3A+XG4gICAgICAgIDxidXR0b24gaWQ9XCIke1JFRlJFU0hfQlROX0lEfS1yZXRyeVwiIGNsYXNzPVwid2lvLXJldHJ5LWJ0blwiPumHjeivlTwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgYCk7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgLy8g5aSE55CG5pyq5Yqg6L2954q25oCBXG4gIGlmICghc3RhdGUuaXNEYXRhTG9hZGVkKSB7XG4gICAgJG1haW5Db250ZW50Lmh0bWwoJzxwIGNsYXNzPVwid2lvLWluZm8tdGV4dFwiPueCueWHu+WIt+aWsOaMiemSruWKoOi9veaVsOaNrjwvcD4nKTtcbiAgICByZXR1cm47XG4gIH1cblxuICBjb25zdCBzZWFyY2hUZXJtID0gc3RhdGUuc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgY29uc3QgJHNlYXJjaElucHV0ID0gJChgIyR7U0VBUkNIX0lOUFVUX0lEfWAsIHBhcmVudERvYyk7XG4gIGlmICgkc2VhcmNoSW5wdXQudmFsKCkgIT09IHN0YXRlLnNlYXJjaFF1ZXJ5KSB7XG4gICAgJHNlYXJjaElucHV0LnZhbChzdGF0ZS5zZWFyY2hRdWVyeSk7XG4gIH1cblxuICBzd2l0Y2ggKHN0YXRlLmFjdGl2ZVRhYikge1xuICAgIGNhc2UgJ2dsb2JhbC1sb3JlJzpcbiAgICAgICRtYWluQ29udGVudC5odG1sKHJlbmRlckdsb2JhbExvcmVib29rVmlldyhzdGF0ZSwgc2VhcmNoVGVybSkpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnY2hhci1sb3JlJzpcbiAgICAgICRtYWluQ29udGVudC5odG1sKHJlbmRlckNoYXJhY3RlckxvcmVib29rVmlldyhzdGF0ZSwgc2VhcmNoVGVybSkpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnY2hhdC1sb3JlJzpcbiAgICAgICRtYWluQ29udGVudC5odG1sKHJlbmRlckNoYXRMb3JlYm9va1ZpZXcoc3RhdGUsIHNlYXJjaFRlcm0pKTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ2dsb2JhbC1yZWdleCc6XG4gICAgICAkbWFpbkNvbnRlbnQuaHRtbChyZW5kZXJSZWdleFZpZXcoc3RhdGUucmVnZXhlcy5nbG9iYWwsIHNlYXJjaFRlcm0sICflhajlsYDmraPliJknLCAnZ2xvYmFsJykpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnY2hhci1yZWdleCc6XG4gICAgICAkbWFpbkNvbnRlbnQuaHRtbChyZW5kZXJSZWdleFZpZXcoc3RhdGUucmVnZXhlcy5jaGFyYWN0ZXIsIHNlYXJjaFRlcm0sICfop5LoibLmraPliJknLCAnY2hhcmFjdGVyJykpO1xuICAgICAgYnJlYWs7XG4gICAgZGVmYXVsdDpcbiAgICAgICRtYWluQ29udGVudC5odG1sKGA8cD7mnKrnn6Xop4blm746ICR7c3RhdGUuYWN0aXZlVGFifTwvcD5gKTtcbiAgfVxufTtcblxuLy8gLS0tIFVJIEluaXRpYWxpemF0aW9uIC0tLVxuXG5leHBvcnQgY29uc3QgaW5pdFVJID0gKCkgPT4ge1xuICBzdWJzY3JpYmUocmVuZGVyKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/elements.ts": 
  /*!*************************************************!*\
  !*** ./src/world_info_optimizer/ui/elements.ts ***!
  \*************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEntryElement: () => (/* binding */ createEntryElement),\n/* harmony export */   createLorebookElement: () => (/* binding */ createLorebookElement),\n/* harmony export */   createRegexItemElement: () => (/* binding */ createRegexItemElement)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ "./src/world_info_optimizer/ui/helpers.ts");\n// src/world_info_optimizer/ui/elements.ts\n\nconst createLorebookElement = (book, state, searchTerm, isGlobal = true, entriesToShow) => {\n    const entries = entriesToShow !== undefined ? entriesToShow : state.lorebookEntries.get(book.name) || [];\n    const totalEntries = state.lorebookEntries.get(book.name)?.length || 0;\n    const usage = state.lorebookUsage.get(book.name) || [];\n    const isCollapsed = state.collapsedBooks.has(book.name);\n    const entryCountText = searchTerm && entries.length !== totalEntries ? `${entries.length} / ${totalEntries}` : `${totalEntries}`;\n    // 折叠图标\n    const collapseIcon = isCollapsed ? \'▶\' : \'▼\';\n    return `\n        <div class="wio-book-group" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">\n            <div class="wio-book-header">\n                <button class="wio-collapse-toggle" title="${isCollapsed ? \'展开\' : \'折叠\'}" aria-label="${isCollapsed ? \'展开\' : \'折叠\'} ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">${collapseIcon}</button>\n                ${isGlobal ? `<input type="checkbox" ${book.enabled ? \'checked\' : \'\'} class="wio-global-book-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">` : \'\'}\n                <h4>${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(book.name, searchTerm)} <span class="wio-entry-count">(${entryCountText})</span></h4>\n                ${usage.length > 0 ? `<span class="wio-usage-pill" title="被 ${usage.join(\', \')} 使用" aria-label="被 ${usage.length} 个角色使用">${usage.length}</span>` : \'\'}\n                <div class="wio-item-controls">\n                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-pencil"></i></button>\n                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-trash-can"></i></button>\n                </div>\n            </div>\n            ${!isCollapsed\n        ? `<div class="wio-entry-list">\n                ${entries.map(entry => createEntryElement(entry, book.name, searchTerm, state)).join(\'\')}\n                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}" aria-label="在 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)} 中新建条目">+ 新建条目</button></div>\n            </div>`\n        : \'\'}\n        </div>\n    `;\n};\nconst createEntryElement = (entry, bookName, searchTerm, state) => {\n    if (!entry)\n        return \'\'; // 代码加固\n    const displayName = entry.comment || \'(未命名条目)\';\n    const itemId = `${bookName}/${entry.uid}`;\n    const isSelected = state.selectedItems.has(itemId);\n    return `\n        <div class="wio-entry-item" data-uid="${entry.uid}" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(bookName)}" data-item-id="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(itemId)}">\n            <div class="wio-entry-main">\n                ${state.multiSelectMode ? `<input type="checkbox" ${isSelected ? \'checked\' : \'\'} class="wio-multi-select-checkbox" aria-label="选择 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">` : \'\'}\n                <input type="checkbox" ${entry.enabled ? \'checked\' : \'\'} class="wio-entry-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-entry-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)}</span>\n                <span class="wio-entry-keys">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)((entry.keys || []).join(\', \'), searchTerm)}</span>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\nconst createRegexItemElement = (regex, searchTerm) => {\n    if (!regex)\n        return \'\'; // 代码加固\n    const displayName = regex.script_name || \'(未命名正则)\';\n    const regexId = regex.id || `${regex.scope}-${btoa(unescape(encodeURIComponent(regex.script_name + regex.find_regex)))}`;\n    return `\n        <div class="wio-regex-item" data-id="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regexId)}" data-scope="${regex.scope}">\n            <div class="wio-regex-main">\n                <input type="checkbox" ${regex.enabled ? \'checked\' : \'\'} class="wio-regex-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-regex-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)} ${regex.source === \'card\' ? \'<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>\' : \'\'}</span>\n                <code class="wio-regex-find" title="查找: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.find_regex, searchTerm)}</code>\n                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>\n                <code class="wio-regex-replace" title="替换为: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.replace_string, searchTerm)}</code>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvdWkvZWxlbWVudHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLDBDQUEwQztBQUdZO0FBRS9DLE1BQU0scUJBQXFCLEdBQUcsQ0FDbkMsSUFBa0IsRUFDbEIsS0FBZSxFQUNmLFVBQWtCLEVBQ2xCLFdBQW9CLElBQUksRUFDeEIsYUFBK0IsRUFDdkIsRUFBRTtJQUNWLE1BQU0sT0FBTyxHQUFHLGFBQWEsS0FBSyxTQUFTLENBQUMsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUN6RyxNQUFNLFlBQVksR0FBRyxLQUFLLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsTUFBTSxJQUFJLENBQUMsQ0FBQztJQUN2RSxNQUFNLEtBQUssR0FBRyxLQUFLLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDO0lBQ3ZELE1BQU0sV0FBVyxHQUFHLEtBQUssQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUV4RCxNQUFNLGNBQWMsR0FDbEIsVUFBVSxJQUFJLE9BQU8sQ0FBQyxNQUFNLEtBQUssWUFBWSxDQUFDLENBQUMsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxNQUFNLE1BQU0sWUFBWSxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsWUFBWSxFQUFFLENBQUM7SUFFNUcsT0FBTztJQUNQLE1BQU0sWUFBWSxHQUFHLFdBQVcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7SUFFN0MsT0FBTztzREFDNkMsb0RBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDOzs2REFFZCxXQUFXLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxpQkFBaUIsV0FBVyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxvREFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxZQUFZO2tCQUN4SixRQUFRLENBQUMsQ0FBQyxDQUFDLDBCQUEwQixJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQUUsa0RBQWtELG9EQUFVLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUU7c0JBQzlJLHVEQUFhLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxVQUFVLENBQUMsbUNBQW1DLGNBQWM7a0JBQ3pGLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyx5Q0FBeUMsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsc0JBQXNCLEtBQUssQ0FBQyxNQUFNLFdBQVcsS0FBSyxDQUFDLE1BQU0sU0FBUyxDQUFDLENBQUMsQ0FBQyxFQUFFOztzRkFFL0Usb0RBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDO29GQUN2QixvREFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUM7OztjQUkzRixDQUFDLFdBQVc7UUFDVixDQUFDLENBQUM7a0JBQ0EsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLGtCQUFrQixDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsSUFBSSxFQUFFLFVBQVUsRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7c0dBQ0Ysb0RBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFtQixvREFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUM7bUJBQ2hKO1FBQ0gsQ0FBQyxDQUFDLEVBQ047O0tBRVAsQ0FBQztBQUNOLENBQUMsQ0FBQztBQUVLLE1BQU0sa0JBQWtCLEdBQUcsQ0FDaEMsS0FBb0IsRUFDcEIsUUFBZ0IsRUFDaEIsVUFBa0IsRUFDbEIsS0FBZSxFQUNQLEVBQUU7SUFDVixJQUFJLENBQUMsS0FBSztRQUFFLE9BQU8sRUFBRSxDQUFDLENBQUMsT0FBTztJQUM5QixNQUFNLFdBQVcsR0FBRyxLQUFLLENBQUMsT0FBTyxJQUFJLFNBQVMsQ0FBQztJQUMvQyxNQUFNLE1BQU0sR0FBRyxHQUFHLFFBQVEsSUFBSSxLQUFLLENBQUMsR0FBRyxFQUFFLENBQUM7SUFDMUMsTUFBTSxVQUFVLEdBQUcsS0FBSyxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUM7SUFFbkQsT0FBTztnREFDdUMsS0FBSyxDQUFDLEdBQUcscUJBQXFCLG9EQUFVLENBQUMsUUFBUSxDQUFDLG1CQUFtQixvREFBVSxDQUFDLE1BQU0sQ0FBQzs7a0JBRXJILEtBQUssQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLDBCQUEwQixVQUFVLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRSxxREFBcUQsb0RBQVUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFO3lDQUMzSSxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQUUsNENBQTRDLG9EQUFVLENBQUMsV0FBVyxDQUFDOytDQUMzRix1REFBYSxDQUFDLFdBQVcsRUFBRSxVQUFVLENBQUM7K0NBQ3RDLHVEQUFhLENBQUMsQ0FBQyxLQUFLLENBQUMsSUFBSSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxVQUFVLENBQUM7OzsrRUFHeEIsb0RBQVUsQ0FBQyxXQUFXLENBQUM7aUZBQ3JCLG9EQUFVLENBQUMsV0FBVyxDQUFDOzs7S0FHbkcsQ0FBQztBQUNOLENBQUMsQ0FBQztBQUVLLE1BQU0sc0JBQXNCLEdBQUcsQ0FBQyxLQUFrQixFQUFFLFVBQWtCLEVBQVUsRUFBRTtJQUN2RixJQUFJLENBQUMsS0FBSztRQUFFLE9BQU8sRUFBRSxDQUFDLENBQUMsT0FBTztJQUM5QixNQUFNLFdBQVcsR0FBRyxLQUFLLENBQUMsV0FBVyxJQUFJLFNBQVMsQ0FBQztJQUNuRCxNQUFNLE9BQU8sR0FDWCxLQUFLLENBQUMsRUFBRSxJQUFJLEdBQUcsS0FBSyxDQUFDLEtBQUssSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLEtBQUssQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO0lBRTNHLE9BQU87K0NBQ3NDLG9EQUFVLENBQUMsT0FBTyxDQUFDLGlCQUFpQixLQUFLLENBQUMsS0FBSzs7eUNBRXJELEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRSw0Q0FBNEMsb0RBQVUsQ0FBQyxXQUFXLENBQUM7K0NBQzNGLHVEQUFhLENBQUMsV0FBVyxFQUFFLFVBQVUsQ0FBQyxJQUFJLEtBQUssQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxtRUFBbUUsQ0FBQyxDQUFDLENBQUMsRUFBRTswREFDakksb0RBQVUsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLEtBQUssdURBQWEsQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLFVBQVUsQ0FBQzs7OERBRXhFLG9EQUFVLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxLQUFLLHVEQUFhLENBQUMsS0FBSyxDQUFDLGNBQWMsRUFBRSxVQUFVLENBQUM7OzsrRUFHbkUsb0RBQVUsQ0FBQyxXQUFXLENBQUM7aUZBQ3JCLG9EQUFVLENBQUMsV0FBVyxDQUFDOzs7S0FHbkcsQ0FBQztBQUNOLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvdWkvZWxlbWVudHMudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy93b3JsZF9pbmZvX29wdGltaXplci91aS9lbGVtZW50cy50c1xuXG5pbXBvcnQgeyBBcHBTdGF0ZSwgTG9yZWJvb2tFbnRyeSwgTG9yZWJvb2tGaWxlLCBUYXZlcm5SZWdleCB9IGZyb20gJy4uL3R5cGVzJztcbmltcG9ydCB7IGVzY2FwZUh0bWwsIGhpZ2hsaWdodFRleHQgfSBmcm9tICcuL2hlbHBlcnMnO1xuXG5leHBvcnQgY29uc3QgY3JlYXRlTG9yZWJvb2tFbGVtZW50ID0gKFxuICBib29rOiBMb3JlYm9va0ZpbGUsXG4gIHN0YXRlOiBBcHBTdGF0ZSxcbiAgc2VhcmNoVGVybTogc3RyaW5nLFxuICBpc0dsb2JhbDogYm9vbGVhbiA9IHRydWUsXG4gIGVudHJpZXNUb1Nob3c/OiBMb3JlYm9va0VudHJ5W10sXG4pOiBzdHJpbmcgPT4ge1xuICBjb25zdCBlbnRyaWVzID0gZW50cmllc1RvU2hvdyAhPT0gdW5kZWZpbmVkID8gZW50cmllc1RvU2hvdyA6IHN0YXRlLmxvcmVib29rRW50cmllcy5nZXQoYm9vay5uYW1lKSB8fCBbXTtcbiAgY29uc3QgdG90YWxFbnRyaWVzID0gc3RhdGUubG9yZWJvb2tFbnRyaWVzLmdldChib29rLm5hbWUpPy5sZW5ndGggfHwgMDtcbiAgY29uc3QgdXNhZ2UgPSBzdGF0ZS5sb3JlYm9va1VzYWdlLmdldChib29rLm5hbWUpIHx8IFtdO1xuICBjb25zdCBpc0NvbGxhcHNlZCA9IHN0YXRlLmNvbGxhcHNlZEJvb2tzLmhhcyhib29rLm5hbWUpO1xuXG4gIGNvbnN0IGVudHJ5Q291bnRUZXh0ID1cbiAgICBzZWFyY2hUZXJtICYmIGVudHJpZXMubGVuZ3RoICE9PSB0b3RhbEVudHJpZXMgPyBgJHtlbnRyaWVzLmxlbmd0aH0gLyAke3RvdGFsRW50cmllc31gIDogYCR7dG90YWxFbnRyaWVzfWA7XG5cbiAgLy8g5oqY5Y+g5Zu+5qCHXG4gIGNvbnN0IGNvbGxhcHNlSWNvbiA9IGlzQ29sbGFwc2VkID8gJ+KWticgOiAn4pa8JztcblxuICByZXR1cm4gYFxuICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLWJvb2stZ3JvdXBcIiBkYXRhLWJvb2stbmFtZT1cIiR7ZXNjYXBlSHRtbChib29rLm5hbWUpfVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzcz1cIndpby1ib29rLWhlYWRlclwiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3aW8tY29sbGFwc2UtdG9nZ2xlXCIgdGl0bGU9XCIke2lzQ29sbGFwc2VkID8gJ+WxleW8gCcgOiAn5oqY5Y+gJ31cIiBhcmlhLWxhYmVsPVwiJHtpc0NvbGxhcHNlZCA/ICflsZXlvIAnIDogJ+aKmOWPoCd9ICR7ZXNjYXBlSHRtbChib29rLm5hbWUpfVwiPiR7Y29sbGFwc2VJY29ufTwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICR7aXNHbG9iYWwgPyBgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiICR7Ym9vay5lbmFibGVkID8gJ2NoZWNrZWQnIDogJyd9IGNsYXNzPVwid2lvLWdsb2JhbC1ib29rLXRvZ2dsZVwiIGFyaWEtbGFiZWw9XCLlkK/nlKggJHtlc2NhcGVIdG1sKGJvb2submFtZSl9XCI+YCA6ICcnfVxuICAgICAgICAgICAgICAgIDxoND4ke2hpZ2hsaWdodFRleHQoYm9vay5uYW1lLCBzZWFyY2hUZXJtKX0gPHNwYW4gY2xhc3M9XCJ3aW8tZW50cnktY291bnRcIj4oJHtlbnRyeUNvdW50VGV4dH0pPC9zcGFuPjwvaDQ+XG4gICAgICAgICAgICAgICAgJHt1c2FnZS5sZW5ndGggPiAwID8gYDxzcGFuIGNsYXNzPVwid2lvLXVzYWdlLXBpbGxcIiB0aXRsZT1cIuiiqyAke3VzYWdlLmpvaW4oJywgJyl9IOS9v+eUqFwiIGFyaWEtbGFiZWw9XCLooqsgJHt1c2FnZS5sZW5ndGh9IOS4quinkuiJsuS9v+eUqFwiPiR7dXNhZ2UubGVuZ3RofTwvc3Bhbj5gIDogJyd9XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cIndpby1pdGVtLWNvbnRyb2xzXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3aW8tcmVuYW1lLWJvb2stYnRuXCIgdGl0bGU9XCLph43lkb3lkI1cIiBhcmlhLWxhYmVsPVwi6YeN5ZG95ZCNICR7ZXNjYXBlSHRtbChib29rLm5hbWUpfVwiPjxpIGNsYXNzPVwiZmEtc29saWQgZmEtcGVuY2lsXCI+PC9pPjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzPVwid2lvLWRlbGV0ZS1ib29rLWJ0blwiIHRpdGxlPVwi5Yig6ZmkXCIgYXJpYS1sYWJlbD1cIuWIoOmZpCAke2VzY2FwZUh0bWwoYm9vay5uYW1lKX1cIj48aSBjbGFzcz1cImZhLXNvbGlkIGZhLXRyYXNoLWNhblwiPjwvaT48L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgJHtcbiAgICAgICAgICAgICAgIWlzQ29sbGFwc2VkXG4gICAgICAgICAgICAgICAgPyBgPGRpdiBjbGFzcz1cIndpby1lbnRyeS1saXN0XCI+XG4gICAgICAgICAgICAgICAgJHtlbnRyaWVzLm1hcChlbnRyeSA9PiBjcmVhdGVFbnRyeUVsZW1lbnQoZW50cnksIGJvb2submFtZSwgc2VhcmNoVGVybSwgc3RhdGUpKS5qb2luKCcnKX1cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLWVudHJ5LWFjdGlvbnNcIj48YnV0dG9uIGNsYXNzPVwid2lvLWNyZWF0ZS1lbnRyeS1idG5cIiBkYXRhLWJvb2stbmFtZT1cIiR7ZXNjYXBlSHRtbChib29rLm5hbWUpfVwiIGFyaWEtbGFiZWw9XCLlnKggJHtlc2NhcGVIdG1sKGJvb2submFtZSl9IOS4reaWsOW7uuadoeebrlwiPisg5paw5bu65p2h55uuPC9idXR0b24+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5gXG4gICAgICAgICAgICAgICAgOiAnJ1xuICAgICAgICAgICAgfVxuICAgICAgICA8L2Rpdj5cbiAgICBgO1xufTtcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZUVudHJ5RWxlbWVudCA9IChcbiAgZW50cnk6IExvcmVib29rRW50cnksXG4gIGJvb2tOYW1lOiBzdHJpbmcsXG4gIHNlYXJjaFRlcm06IHN0cmluZyxcbiAgc3RhdGU6IEFwcFN0YXRlLFxuKTogc3RyaW5nID0+IHtcbiAgaWYgKCFlbnRyeSkgcmV0dXJuICcnOyAvLyDku6PnoIHliqDlm7pcbiAgY29uc3QgZGlzcGxheU5hbWUgPSBlbnRyeS5jb21tZW50IHx8ICco5pyq5ZG95ZCN5p2h55uuKSc7XG4gIGNvbnN0IGl0ZW1JZCA9IGAke2Jvb2tOYW1lfS8ke2VudHJ5LnVpZH1gO1xuICBjb25zdCBpc1NlbGVjdGVkID0gc3RhdGUuc2VsZWN0ZWRJdGVtcy5oYXMoaXRlbUlkKTtcblxuICByZXR1cm4gYFxuICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLWVudHJ5LWl0ZW1cIiBkYXRhLXVpZD1cIiR7ZW50cnkudWlkfVwiIGRhdGEtYm9vay1uYW1lPVwiJHtlc2NhcGVIdG1sKGJvb2tOYW1lKX1cIiBkYXRhLWl0ZW0taWQ9XCIke2VzY2FwZUh0bWwoaXRlbUlkKX1cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ3aW8tZW50cnktbWFpblwiPlxuICAgICAgICAgICAgICAgICR7c3RhdGUubXVsdGlTZWxlY3RNb2RlID8gYDxpbnB1dCB0eXBlPVwiY2hlY2tib3hcIiAke2lzU2VsZWN0ZWQgPyAnY2hlY2tlZCcgOiAnJ30gY2xhc3M9XCJ3aW8tbXVsdGktc2VsZWN0LWNoZWNrYm94XCIgYXJpYS1sYWJlbD1cIumAieaLqSAke2VzY2FwZUh0bWwoZGlzcGxheU5hbWUpfVwiPmAgOiAnJ31cbiAgICAgICAgICAgICAgICA8aW5wdXQgdHlwZT1cImNoZWNrYm94XCIgJHtlbnRyeS5lbmFibGVkID8gJ2NoZWNrZWQnIDogJyd9IGNsYXNzPVwid2lvLWVudHJ5LXRvZ2dsZVwiIGFyaWEtbGFiZWw9XCLlkK/nlKggJHtlc2NhcGVIdG1sKGRpc3BsYXlOYW1lKX1cIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz1cIndpby1lbnRyeS1uYW1lXCI+JHtoaWdobGlnaHRUZXh0KGRpc3BsYXlOYW1lLCBzZWFyY2hUZXJtKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9XCJ3aW8tZW50cnkta2V5c1wiPiR7aGlnaGxpZ2h0VGV4dCgoZW50cnkua2V5cyB8fCBbXSkuam9pbignLCAnKSwgc2VhcmNoVGVybSl9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLWl0ZW0tY29udHJvbHNcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzPVwid2lvLWVkaXQtZW50cnktYnRuXCIgdGl0bGU9XCLnvJbovpFcIiBhcmlhLWxhYmVsPVwi57yW6L6RICR7ZXNjYXBlSHRtbChkaXNwbGF5TmFtZSl9XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS1wZW5jaWxcIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzcz1cIndpby1kZWxldGUtZW50cnktYnRuXCIgdGl0bGU9XCLliKDpmaRcIiBhcmlhLWxhYmVsPVwi5Yig6ZmkICR7ZXNjYXBlSHRtbChkaXNwbGF5TmFtZSl9XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS10cmFzaC1jYW5cIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgYDtcbn07XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVSZWdleEl0ZW1FbGVtZW50ID0gKHJlZ2V4OiBUYXZlcm5SZWdleCwgc2VhcmNoVGVybTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgaWYgKCFyZWdleCkgcmV0dXJuICcnOyAvLyDku6PnoIHliqDlm7pcbiAgY29uc3QgZGlzcGxheU5hbWUgPSByZWdleC5zY3JpcHRfbmFtZSB8fCAnKOacquWRveWQjeato+WImSknO1xuICBjb25zdCByZWdleElkID1cbiAgICByZWdleC5pZCB8fCBgJHtyZWdleC5zY29wZX0tJHtidG9hKHVuZXNjYXBlKGVuY29kZVVSSUNvbXBvbmVudChyZWdleC5zY3JpcHRfbmFtZSArIHJlZ2V4LmZpbmRfcmVnZXgpKSl9YDtcblxuICByZXR1cm4gYFxuICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLXJlZ2V4LWl0ZW1cIiBkYXRhLWlkPVwiJHtlc2NhcGVIdG1sKHJlZ2V4SWQpfVwiIGRhdGEtc2NvcGU9XCIke3JlZ2V4LnNjb3BlfVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzcz1cIndpby1yZWdleC1tYWluXCI+XG4gICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiICR7cmVnZXguZW5hYmxlZCA/ICdjaGVja2VkJyA6ICcnfSBjbGFzcz1cIndpby1yZWdleC10b2dnbGVcIiBhcmlhLWxhYmVsPVwi5ZCv55SoICR7ZXNjYXBlSHRtbChkaXNwbGF5TmFtZSl9XCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9XCJ3aW8tcmVnZXgtbmFtZVwiPiR7aGlnaGxpZ2h0VGV4dChkaXNwbGF5TmFtZSwgc2VhcmNoVGVybSl9ICR7cmVnZXguc291cmNlID09PSAnY2FyZCcgPyAnPHNwYW4gY2xhc3M9XCJ3aW8tcmVnZXgtc291cmNlLWJhZGdlXCIgYXJpYS1sYWJlbD1cIuWNoeWGheato+WImVwiPijljaEpPC9zcGFuPicgOiAnJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgPGNvZGUgY2xhc3M9XCJ3aW8tcmVnZXgtZmluZFwiIHRpdGxlPVwi5p+l5om+OiAke2VzY2FwZUh0bWwocmVnZXguZmluZF9yZWdleCl9XCI+JHtoaWdobGlnaHRUZXh0KHJlZ2V4LmZpbmRfcmVnZXgsIHNlYXJjaFRlcm0pfTwvY29kZT5cbiAgICAgICAgICAgICAgICA8aSBjbGFzcz1cImZhLXNvbGlkIGZhLWFycm93LXJpZ2h0LWxvbmdcIiBhcmlhLWhpZGRlbj1cInRydWVcIj48L2k+XG4gICAgICAgICAgICAgICAgPGNvZGUgY2xhc3M9XCJ3aW8tcmVnZXgtcmVwbGFjZVwiIHRpdGxlPVwi5pu/5o2i5Li6OiAke2VzY2FwZUh0bWwocmVnZXgucmVwbGFjZV9zdHJpbmcpfVwiPiR7aGlnaGxpZ2h0VGV4dChyZWdleC5yZXBsYWNlX3N0cmluZywgc2VhcmNoVGVybSl9PC9jb2RlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2lvLWl0ZW0tY29udHJvbHNcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzPVwid2lvLWVkaXQtcmVnZXgtYnRuXCIgdGl0bGU9XCLnvJbovpFcIiBhcmlhLWxhYmVsPVwi57yW6L6RICR7ZXNjYXBlSHRtbChkaXNwbGF5TmFtZSl9XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS1wZW5jaWxcIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzcz1cIndpby1kZWxldGUtcmVnZXgtYnRuXCIgdGl0bGU9XCLliKDpmaRcIiBhcmlhLWxhYmVsPVwi5Yig6ZmkICR7ZXNjYXBlSHRtbChkaXNwbGF5TmFtZSl9XCI+PGkgY2xhc3M9XCJmYS1zb2xpZCBmYS10cmFzaC1jYW5cIj48L2k+PC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgYDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/elements.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/helpers.ts": 
  /*!************************************************!*\
  !*** ./src/world_info_optimizer/ui/helpers.ts ***!
  \************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   escapeHtml: () => (/* binding */ escapeHtml),\n/* harmony export */   highlightText: () => (/* binding */ highlightText)\n/* harmony export */ });\n// src/world_info_optimizer/ui/helpers.ts\nconst escapeHtml = (text) => {\n    if (typeof text !== 'string')\n        text = String(text);\n    const p = document.createElement('p');\n    p.textContent = text;\n    return p.innerHTML;\n};\nconst highlightText = (text, searchTerm) => {\n    if (!searchTerm || !text)\n        return escapeHtml(text);\n    const escapedText = escapeHtml(text);\n    const htmlSafeSearchTerm = escapeHtml(searchTerm);\n    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n    return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/helpers.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/modals.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/ui/modals.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   showEntryEditorModal: () => (/* binding */ showEntryEditorModal),\n/* harmony export */   showModal: () => (/* binding */ showModal),\n/* harmony export */   showProgressToast: () => (/* binding */ showProgressToast),\n/* harmony export */   showRegexEditorModal: () => (/* binding */ showRegexEditorModal),\n/* harmony export */   showSuccessTick: () => (/* binding */ showSuccessTick)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/modals.ts\n\nconst showModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return new Promise((resolve, reject) => {\n            const result = window.parent.prompt(options.text, typeof options.value === 'string' ? options.value : '');\n            if (result !== null)\n                resolve(result);\n            else\n                reject(new Error('Prompt cancelled'));\n        });\n    }\n    const type = options.type || 'alert';\n    switch (type) {\n        case 'confirm':\n            return Swal.fire({\n                title: options.title || '确认',\n                text: options.text,\n                icon: 'warning',\n                showCancelButton: true,\n                confirmButtonText: '确认',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed) {\n                    return true;\n                }\n                throw new Error('Confirmation cancelled');\n            });\n        case 'prompt':\n            return Swal.fire({\n                title: options.title,\n                text: options.text,\n                input: 'text',\n                inputValue: options.value || '',\n                showCancelButton: true,\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed && typeof result.value === 'string') {\n                    return result.value;\n                }\n                throw new Error('Prompt cancelled');\n            });\n        case 'alert':\n        default:\n            return Swal.fire({\n                title: options.title || '提示',\n                text: options.text,\n                icon: 'info',\n            }).then(() => true);\n    }\n};\nconst showSuccessTick = (message = '操作成功', duration = 1500) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[SUCCESS] ${message}`);\n        return;\n    }\n    const Toast = Swal.mixin({\n        toast: true,\n        position: 'top-end',\n        showConfirmButton: false,\n        timer: duration,\n        timerProgressBar: true,\n        didOpen: (toast) => {\n            toast.addEventListener('mouseenter', Swal.stopTimer);\n            toast.addEventListener('mouseleave', Swal.resumeTimer);\n        },\n    });\n    Toast.fire({\n        icon: 'success',\n        title: message,\n    });\n};\nconst showEntryEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { entry, isCreating, bookName } = options;\n    const title = isCreating ? `在 \"${bookName}\" 中创建新条目` : `编辑条目: ${entry.comment}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-comment\" class=\"swal2-label\">注释 (条目名)</label>\n                <input id=\"swal-comment\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.comment || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-keys\" class=\"swal2-label\">关键词 (逗号分隔)</label>\n                <input id=\"swal-keys\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)((entry.keys || []).join(', '))}\">\n\n                <label for=\"swal-content\" class=\"swal2-label\">内容</label>\n                <textarea id=\"swal-content\" class=\"swal2-textarea\">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.content || '')}</textarea>\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const comment = document.getElementById('swal-comment').value;\n            const keys = document.getElementById('swal-keys').value;\n            const content = document.getElementById('swal-content').value;\n            if (!comment && !keys) {\n                Swal.showValidationMessage('注释和关键词不能都为空');\n                return false;\n            }\n            return { comment, keys, content };\n        },\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { comment, keys, content } = result.value;\n            return {\n                ...entry,\n                comment,\n                keys: keys\n                    .split(',')\n                    .map((k) => k.trim())\n                    .filter(Boolean),\n                content,\n            };\n        }\n        throw new Error('Entry editor cancelled');\n    });\n};\nconst showProgressToast = (initialMessage = '正在处理...') => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[PROGRESS] ${initialMessage}`);\n        return {\n            update: (newMessage) => console.log(`[PROGRESS UPDATE] ${newMessage}`),\n            remove: () => console.log(`[PROGRESS] Done.`),\n        };\n    }\n    Swal.fire({\n        toast: true,\n        position: 'bottom-end',\n        title: initialMessage,\n        showConfirmButton: false,\n        didOpen: () => {\n            Swal.showLoading();\n        },\n    });\n    return {\n        update: (newMessage) => {\n            Swal.update({\n                title: newMessage,\n            });\n        },\n        remove: () => {\n            Swal.close();\n        },\n    };\n};\nconst showRegexEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { regex, isCreating } = options;\n    const title = isCreating ? '创建新正则' : `编辑正则: ${regex.script_name}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-script-name\" class=\"swal2-label\">名称</label>\n                <input id=\"swal-script-name\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.script_name || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-find-regex\" class=\"swal2-label\">查找 (正则表达式)</label>\n                <input id=\"swal-find-regex\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-replace-string\" class=\"swal2-label\">替换为</label>\n                <input id=\"swal-replace-string\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string || '')}\">\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const script_name = document.getElementById('swal-script-name').value;\n            const find_regex = document.getElementById('swal-find-regex').value;\n            const replace_string = document.getElementById('swal-replace-string').value;\n            if (!script_name || !find_regex) {\n                Swal.showValidationMessage('名称和查找正则不能为空');\n                return false;\n            }\n            return { script_name, find_regex, replace_string };\n        },\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { script_name, find_regex, replace_string } = result.value;\n            return {\n                ...regex,\n                script_name,\n                find_regex,\n                replace_string,\n            };\n        }\n        throw new Error('Regex editor cancelled');\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/modals.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/views.ts": 
  /*!**********************************************!*\
  !*** ./src/world_info_optimizer/ui/views.ts ***!
  \**********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderCharacterLorebookView: () => (/* binding */ renderCharacterLorebookView),\n/* harmony export */   renderChatLorebookView: () => (/* binding */ renderChatLorebookView),\n/* harmony export */   renderGlobalLorebookView: () => (/* binding */ renderGlobalLorebookView),\n/* harmony export */   renderRegexView: () => (/* binding */ renderRegexView)\n/* harmony export */ });\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./elements */ \"./src/world_info_optimizer/ui/elements.ts\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/views.ts\n\n\n\nconst renderGlobalLorebookView = (state, searchTerm) => {\n    const books = [...state.allLorebooks].sort((a, b) => (b.enabled ? 1 : -1) - (a.enabled ? 1 : -1) || a.name.localeCompare(b.name));\n    if (books.length === 0)\n        return `<p class=\"wio-info-text\">没有找到全局世界书。</p>`;\n    if (!searchTerm) {\n        return books.map(book => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm)).join('');\n    }\n    const filteredBookHtml = books\n        .map(book => {\n        const bookNameMatches = book.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(book.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm, true, entriesToShow);\n        }\n        return '';\n    })\n        .join('');\n    return filteredBookHtml || `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderCharacterLorebookView = (state, searchTerm) => {\n    const linkedBooks = state.lorebooks.character;\n    if (linkedBooks.length === 0)\n        return `<p class=\"wio-info-text\">当前角色没有绑定的世界书。</p>`;\n    if (!searchTerm) {\n        return linkedBooks\n            .map(bookName => {\n            const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false);\n        })\n            .join('');\n    }\n    const filteredBookHtml = linkedBooks\n        .map(bookName => {\n        const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n        const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(bookFile.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow);\n        }\n        return '';\n    })\n        .join('');\n    return filteredBookHtml || `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderChatLorebookView = (state, searchTerm) => {\n    const bookName = state.chatLorebook;\n    if (!bookName)\n        return `<p class=\"wio-info-text\">当前聊天没有绑定的世界书。</p>`;\n    const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n    if (!searchTerm) {\n        return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false);\n    }\n    const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n    const entries = state.lorebookEntries.get(bookFile.name) || [];\n    const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n        (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n        (entry.content || '').toLowerCase().includes(searchTerm));\n    if (bookNameMatches || filteredEntries.length > 0) {\n        const entriesToShow = bookNameMatches ? entries : filteredEntries;\n        return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow);\n    }\n    return `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderRegexView = (regexes, searchTerm, title, scope) => {\n    let content = `<div class=\"wio-regex-group\" data-scope=\"${scope}\"><h3>${title} (${regexes.length})</h3>`;\n    if (regexes.length === 0 && scope === 'character' && !(0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character) {\n        content += `<p class=\"wio-info-text\">没有加载角色，无法显示角色正则。</p>`;\n    }\n    else if (regexes.length === 0) {\n        content += `<p class=\"wio-info-text\">没有找到正则。</p>`;\n    }\n    const filteredRegexes = searchTerm\n        ? regexes.filter(r => (r.script_name || '').toLowerCase().includes(searchTerm) ||\n            (r.find_regex || '').toLowerCase().includes(searchTerm) ||\n            (r.replace_string || '').toLowerCase().includes(searchTerm))\n        : regexes;\n    if (filteredRegexes.length > 0) {\n        content += filteredRegexes.map(r => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createRegexItemElement)(r, searchTerm)).join('');\n    }\n    else if (searchTerm) {\n        content += `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的正则。</p>`;\n    }\n    if (scope === 'global' || (scope === 'character' && (0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character)) {\n        content += `<div class=\"wio-regex-actions\"><button class=\"wio-create-regex-btn\" data-scope=\"${scope}\">+ 新建正则</button></div>`;\n    }\n    return content + '</div>';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/views.ts\n\n}");
  }
};

var __webpack_module_cache__ = {};

function __webpack_require__(moduleId) {
  var cachedModule = __webpack_module_cache__[moduleId];
  if (cachedModule !== undefined) {
    return cachedModule.exports;
  }
  var module = __webpack_module_cache__[moduleId] = {
    exports: {}
  };
  __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
  return module.exports;
}

(() => {
  __webpack_require__.d = (exports, definition) => {
    for (var key in definition) {
      if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
        Object.defineProperty(exports, key, {
          enumerable: true,
          get: definition[key]
        });
      }
    }
  };
})();

(() => {
  __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
})();

(() => {
  __webpack_require__.r = exports => {
    if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: "Module"
      });
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
  };
})();

var __webpack_exports__ = __webpack_require__("./src/world_info_optimizer/index.ts");