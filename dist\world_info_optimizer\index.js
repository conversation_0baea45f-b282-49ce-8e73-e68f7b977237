const e='world-info-optimizer-panel',o='world-info-optimizer-button',n='wio-search-input',t='wio-refresh-btn',r='wio-refresh-character-btn',a='wio-collapse-current-btn',i='wio-create-lorebook-btn',l=e=>{'string'!=typeof e&&(e=String(e));const o=document.createElement('p');return o.textContent=e,o.innerHTML},s=(e,o)=>{if(!o||!e)return l(e);const n=l(e),t=l(o).replace(/[.*+?^${}()|[\]\\]/g,'\\$&'),r=new RegExp(`(${t})`,'gi');return n.replace(r,'<mark class="wio-highlight">$1</mark>')},c=e=>{const o=window.parent.Swal;if(!o)return new Promise((o,n)=>{const t=window.parent.prompt(e.text,'string'==typeof e.value?e.value:'');null!==t?o(t):n(new Error('Prompt cancelled'))});switch(e.type||'alert'){case'confirm':return o.fire({title:e.title||'确认',text:e.text,icon:'warning',showCancelButton:!0,confirmButtonText:'确认',cancelButtonText:'取消'}).then(e=>{if(e.isConfirmed)return!0;throw new Error('Confirmation cancelled')});case'prompt':return o.fire({title:e.title,text:e.text,input:'text',inputValue:e.value||'',showCancelButton:!0,confirmButtonText:'确定',cancelButtonText:'取消'}).then(e=>{if(e.isConfirmed&&'string'==typeof e.value)return e.value;throw new Error('Prompt cancelled')});default:return o.fire({title:e.title||'提示',text:e.text,icon:'info'}).then(()=>!0)}},d=e=>{const o=window.parent.Swal;if(!o)return Promise.reject(new Error('Swal not found!'));const{entry:n,isCreating:t,bookName:r}=e,a=t?`在 "${r}" 中创建新条目`:`编辑条目: ${n.comment}`;return o.fire({title:a,html:`\n            <div style="text-align: left;">\n                <label for="swal-comment" class="swal2-label">注释 (条目名)</label>\n                <input id="swal-comment" class="swal2-input" value="${l(n.comment||'')}" aria-required="true">\n\n                <label for="swal-keys" class="swal2-label">关键词 (逗号分隔)</label>\n                <input id="swal-keys" class="swal2-input" value="${l((n.keys||[]).join(', '))}">\n\n                <label for="swal-content" class="swal2-label">内容</label>\n                <textarea id="swal-content" class="swal2-textarea">${l(n.content||'')}</textarea>\n            </div>\n        `,showCancelButton:!0,confirmButtonText:'保存',cancelButtonText:'取消',preConfirm:()=>{const e=document.getElementById('swal-comment').value,n=document.getElementById('swal-keys').value,t=document.getElementById('swal-content').value;return e||n?{comment:e,keys:n,content:t}:(o.showValidationMessage('注释和关键词不能都为空'),!1)}}).then(e=>{if(e.isConfirmed){const{comment:o,keys:t,content:r}=e.value;return{...n,comment:o,keys:t.split(',').map(e=>e.trim()).filter(Boolean),content:r}}throw new Error('Entry editor cancelled')})},b=e=>{const o=window.parent.Swal;if(!o)return Promise.reject(new Error('Swal not found!'));const{regex:n,isCreating:t}=e,r=t?'创建新正则':`编辑正则: ${n.script_name}`;return o.fire({title:r,html:`\n            <div style="text-align: left;">\n                <label for="swal-script-name" class="swal2-label">名称</label>\n                <input id="swal-script-name" class="swal2-input" value="${l(n.script_name||'')}" aria-required="true">\n\n                <label for="swal-find-regex" class="swal2-label">查找 (正则表达式)</label>\n                <input id="swal-find-regex" class="swal2-input" value="${l(n.find_regex||'')}" aria-required="true">\n\n                <label for="swal-replace-string" class="swal2-label">替换为</label>\n                <input id="swal-replace-string" class="swal2-input" value="${l(n.replace_string||'')}">\n            </div>\n        `,showCancelButton:!0,confirmButtonText:'保存',cancelButtonText:'取消',preConfirm:()=>{const e=document.getElementById('swal-script-name').value,n=document.getElementById('swal-find-regex').value,t=document.getElementById('swal-replace-string').value;return e&&n?{script_name:e,find_regex:n,replace_string:t}:(o.showValidationMessage('名称和查找正则不能为空'),!1)}}).then(e=>{if(e.isConfirmed){const{script_name:o,find_regex:t,replace_string:r}=e.value;return{...n,script_name:o,find_regex:t,replace_string:r}}throw new Error('Regex editor cancelled')})},w=(e,o='TavernAPI')=>async(...n)=>{try{return await e(...n)}catch(e){return e&&(console.error(`[${o}] Error:`,e),await c({type:'alert',title:'API调用异常',text:'操作中发生未知错误，请检查开发者控制台获取详细信息。'})),null}};const g=new class{helper;constructor(){this.helper=(()=>{const e=window.parent;if(!e.TavernHelper)throw new Error('TavernHelper is not available on the parent window.');return e.TavernHelper})()}createLorebook=w(async e=>this.helper.createLorebook(e));deleteLorebook=w(async e=>this.helper.deleteLorebook(e));getLorebooks=w(async()=>this.helper.getLorebooks());getLorebookSettings=w(async()=>this.helper.getLorebookSettings());setLorebookSettings=w(async e=>this.helper.setLorebookSettings(e));getLorebookEntries=w(async e=>this.helper.getLorebookEntries(e));setLorebookEntries=w(async(e,o)=>this.helper.setLorebookEntries(e,o));createLorebookEntries=w(async(e,o)=>this.helper.createLorebookEntries(e,o));deleteLorebookEntries=w(async(e,o)=>this.helper.deleteLorebookEntries(e,o));getCharLorebooks=w(async e=>this.helper.getCharLorebooks(e));getCurrentCharLorebooks=w(async()=>this.helper.getCharLorebooks());setCurrentCharLorebooks=w(async e=>this.helper.setCurrentCharLorebooks(e));getChatLorebook=w(async()=>this.helper.getChatLorebook());setChatLorebook=w(async e=>this.helper.setChatLorebook(e));getOrCreateChatLorebook=w(async e=>this.helper.getOrCreateChatLorebook(e));getRegexes=w(async()=>this.helper.getTavernRegexes({scope:'all'}));replaceRegexes=w(async e=>this.helper.replaceTavernRegexes(e,{scope:'all'}));getCharData=w(async()=>this.helper.getCharData());saveSettings=w(async()=>this.helper.builtin.saveSettings());getContext=()=>(()=>{const e=window.parent;return e.SillyTavern&&'function'==typeof e.SillyTavern.getContext?e.SillyTavern.getContext():(console.warn('[WIO API] SillyTavern.getContext is not available.'),{characters:[],characterId:null,chatId:null})})();get Character(){return this.helper.Character}},p={regexes:{global:[],character:[]},lorebooks:{character:[]},chatLorebook:null,allLorebooks:[],lorebookEntries:new Map,lorebookUsage:new Map,activeTab:'global-lore',isDataLoaded:!1,isLoading:!1,loadError:null,searchFilters:{bookName:!0,entryName:!0,keywords:!0,content:!0},searchQuery:'',multiSelectMode:!1,selectedItems:new Set,collapsedBooks:new Set};let u=JSON.parse(JSON.stringify(p));u.lorebookEntries=new Map,u.lorebookUsage=new Map,u.selectedItems=new Set,u.collapsedBooks=new Set,u.isLoading=!1,u.loadError=null;const h=[],m=()=>{const e={...u,lorebookEntries:new Map(Array.from(u.lorebookEntries.entries()).map(([e,o])=>[e,[...o]])),lorebookUsage:new Map(Array.from(u.lorebookUsage.entries()).map(([e,o])=>[e,[...o]])),selectedItems:new Set(u.selectedItems),collapsedBooks:new Set(u.collapsedBooks),regexes:{global:[...u.regexes.global],character:[...u.regexes.character]},lorebooks:{character:[...u.lorebooks.character]},allLorebooks:[...u.allLorebooks],searchFilters:{...u.searchFilters}};h.forEach(o=>o(e))},f=()=>({...u,lorebookEntries:new Map(Array.from(u.lorebookEntries.entries()).map(([e,o])=>[e,[...o]])),lorebookUsage:new Map(Array.from(u.lorebookUsage.entries()).map(([e,o])=>[e,[...o]])),selectedItems:new Set(u.selectedItems),collapsedBooks:new Set(u.collapsedBooks),regexes:{global:[...u.regexes.global],character:[...u.regexes.character]},lorebooks:{character:[...u.lorebooks.character]},allLorebooks:[...u.allLorebooks],searchFilters:{...u.searchFilters}}),k=e=>{const o=e(u);u={...u,...o},m()},x=async()=>{if(f().isLoading)console.log('[WIO Core] Data loading already in progress, skipping...');else{k(e=>({...e,isDataLoaded:!1,isLoading:!0,loadError:null}));try{const o=g.getContext(),{characters:n,characterId:t,chatId:r}=o,a=null!=t,i=null!=r,l=await Promise.allSettled([g.getRegexes(),g.getLorebookSettings(),g.getLorebooks(),a?g.getCharData():Promise.resolve(null),a?g.getCurrentCharLorebooks():Promise.resolve(null),i?g.getChatLorebook():Promise.resolve(null)]),s='fulfilled'===l[0].status?l[0].value:[],c='fulfilled'===l[1].status?l[1].value:{},d='fulfilled'===l[2].status?l[2].value:[],b='fulfilled'===l[3].status?l[3].value:null,w='fulfilled'===l[4].status?l[4].value:null,p='fulfilled'===l[5].status?l[5].value:null,u=(s||[]).filter(e=>'global'===e.scope),h=y(s,b),m=new Set(c?.selected_global_lorebooks||[]),f=(d||[]).map(e=>({name:e,enabled:m.has(e)})),x=new Set;w?.primary&&x.add(w.primary),w?.additional&&w.additional.forEach(e=>x.add(e));const v=Array.from(x),E=new Map,L=new Set(d||[]);if(Array.isArray(n))for(const e of n){if(!e?.name)continue;const o=await g.getCharLorebooks({name:e.name}),n=new Set;o?.primary&&n.add(o.primary),o?.additional&&o.additional.forEach(e=>n.add(e)),n.forEach(o=>{E.has(o)||E.set(o,[]),E.get(o)?.push(e.name),L.add(o)})}v.forEach(e=>L.add(e)),p&&L.add(p);const C=new Map,I=Array.from(L).map(async e=>{const o=await g.getLorebookEntries(e);o&&C.set(e,o)});await Promise.all(I),e={globalRegexes:u,characterRegexes:h,allLorebooks:f,characterLorebooks:v,chatLorebook:p||null,lorebookEntries:C,lorebookUsage:E},k(o=>({...o,regexes:{global:e.globalRegexes,character:e.characterRegexes},allLorebooks:e.allLorebooks,lorebooks:{character:e.characterLorebooks},chatLorebook:e.chatLorebook,lorebookEntries:e.lorebookEntries,lorebookUsage:e.lorebookUsage,isDataLoaded:!0}))}catch(e){console.error('[WIO Core] Failed to load all data:',e);const o=e instanceof Error?e.message:'未知错误';k(e=>({...e,isDataLoaded:!1,isLoading:!1,loadError:`数据加载失败: ${o}`}))}finally{k(e=>({...e,isLoading:!1}))}var e}};function y(e,o){const n=e?.filter(e=>'character'===e.scope)||[];let t=[];if(o&&g.Character)try{t=(new g.Character(o).getRegexScripts()||[]).map((e,o)=>({id:e.id||`card-${Date.now()}-${o}`,script_name:e.scriptName||'未命名卡内正则',find_regex:e.findRegex,replace_string:e.replaceString,enabled:!e.disabled,scope:'character',source:'card'}))}catch(e){console.warn('[WIO Core] Couldn\'t parse character card regex scripts:',e)}const r=new Set(n.map(e=>`${e.script_name}::${e.find_regex}::${e.replace_string}`)),a=t.filter(e=>{const o=`${e.script_name}::${e.find_regex}::${e.replace_string}`;return!r.has(o)});return[...n,...a]}const v=async(e,o,n)=>{const t=f(),r=t.lorebookEntries.get(e);if(!r)throw new Error(`[WIO Core] Book not found in state: ${e}`);let a=!1;const i=r.map(e=>e.uid===o?(a=!0,{...e,...n}):e);if(a){null!==await g.setLorebookEntries(e,i)&&(t.lorebookEntries.set(e,i),k(e=>({...e,lorebookEntries:new Map(t.lorebookEntries)})))}},E=async e=>{null!==await g.replaceRegexes(e)&&await x()},L=async(e,o)=>{const n=f(),t=[...n.regexes.global.filter(e=>'card'!==e.source),...n.regexes.character.filter(e=>'card'!==e.source)].map(n=>n.id===e?{...n,...o}:n);await E(t)},C=async e=>{const o=f(),n=Array.from(o.selectedItems);if(0===n.length)throw new Error('没有选中任何条目');let t=0;const r=new Map;for(const a of n){const[n,i]=a.split('/');if(n&&i){const a=o.lorebookEntries.get(n)||[],l=a.map(o=>o.uid===i&&o.enabled!==e?(t++,{...o,enabled:e}):o);l.some((e,o)=>e!==a[o])&&r.set(n,l)}}const a=Array.from(r.entries()).map(async([e,n])=>null!==await g.setLorebookEntries(e,n)?()=>o.lorebookEntries.set(e,n):null);return(await Promise.all(a)).filter(Boolean).forEach(e=>e()),o.selectedItems.clear(),k(e=>({...e,lorebookEntries:new Map(o.lorebookEntries),selectedItems:new Set,multiSelectMode:!1})),t};let I,$;const S=l=>{I=l.document,$=l.jQuery;const s=$('body',I);s.on('click',`#${o}`,()=>{$(`#${e}`,I).fadeIn(200),f().isDataLoaded||x()}),s.on('click',`#${e} .wio-close-btn`,()=>{$(`#${e}`,I).fadeOut(200)});const c=$(`#${e}`,I);c.on('click',async e=>{const o=$(e.target),n=o.closest('.wio-tab-btn');if(n.length){return void(e=>{k(o=>({...o,activeTab:e}))})(n.data('tab-id'))}if(o.closest(`#${i}`).length)return void j();if(o.closest(`#${t}`).length||o.closest(`#${t}-retry`).length)return void x();if(o.closest(`#${r}`).length)return void(async()=>{const e=f();if(e.isLoading)console.log('[WIO Core] Data loading already in progress, skipping character refresh...');else{k(e=>({...e,isLoading:!0,loadError:null}));try{const o=g.getContext(),{characterId:n,chatId:t}=o,r=null!=n,a=null!=t,i=await Promise.allSettled([g.getRegexes(),r?g.getCharData():Promise.resolve(null),r?g.getCurrentCharLorebooks():Promise.resolve(null),a?g.getChatLorebook():Promise.resolve(null)]),l='fulfilled'===i[0].status?i[0].value:[],s='fulfilled'===i[1].status?i[1].value:null,c='fulfilled'===i[2].status?i[2].value:null,d='fulfilled'===i[3].status?i[3].value:null,b=y(l,s),w=new Set;c?.primary&&w.add(c.primary),c?.additional&&c.additional.forEach(e=>w.add(e));const p=Array.from(w),u=new Set;p.forEach(e=>u.add(e)),d&&u.add(d);const h=new Map(e.lorebookEntries),m=Array.from(u).map(async e=>{const o=await g.getLorebookEntries(e);o&&h.set(e,o)});await Promise.all(m),k(e=>({...e,regexes:{...e.regexes,character:b},lorebooks:{character:p},chatLorebook:d||null,lorebookEntries:h,isDataLoaded:!0,isLoading:!1})),console.log('[WIO Core] Character data refreshed successfully')}catch(e){console.error('[WIO Core] Failed to refresh character data:',e);const o=e instanceof Error?e.message:'未知错误';k(e=>({...e,isLoading:!1,loadError:`角色数据刷新失败: ${o}`}))}}})();if(o.closest('#wio-bulk-replace-btn').length)return void R();if(o.closest('#wio-multi-select-btn').length)return void k(e=>({...e,multiSelectMode:!e.multiSelectMode,selectedItems:new Set}));if(o.closest('#wio-bulk-delete-btn').length)return void F();if(o.closest('#wio-bulk-enable-btn').length)return void U();if(o.closest('#wio-bulk-disable-btn').length)return void N();if(o.closest(`#${a}`).length)return void k(e=>{const o=new Set;if('char-lore'===e.activeTab?e.lorebooks.character.forEach(e=>o.add(e)):'chat-lore'===e.activeTab&&e.chatLorebook&&o.add(e.chatLorebook),0===o.size)return e;const n=new Set(e.collapsedBooks);return o.forEach(e=>n.add(e)),{...e,collapsedBooks:n}});if(o.closest('#wio-collapse-all-btn').length)return void(()=>{const e=f(),o=new Set;e.allLorebooks.forEach(e=>o.add(e.name)),e.lorebooks.character.forEach(e=>o.add(e)),e.chatLorebook&&o.add(e.chatLorebook),k(e=>({...e,collapsedBooks:o}))})();if(o.closest('#wio-expand-all-btn').length)return void k(e=>({...e,collapsedBooks:new Set}));const l=o.closest('.wio-collapse-toggle');if(l.length){return void(e=>{k(o=>{const n=new Set(o.collapsedBooks);return n.has(e)?n.delete(e):n.add(e),{...o,collapsedBooks:n}})})(l.closest('.wio-book-group').data('book-name'))}const s=o.closest('.wio-rename-book-btn');if(s.length){const e=s.closest('.wio-book-group').data('book-name');return void B(e)}const c=o.closest('.wio-delete-book-btn');if(c.length){const e=c.closest('.wio-book-group').data('book-name');return void T(e)}const d=o.closest('.wio-create-entry-btn');if(d.length){const e=d.data('book-name');return void M(e)}const b=o.closest('.wio-edit-entry-btn');if(b.length){const e=b.closest('.wio-entry-item'),o=e.data('book-name'),n=e.data('uid');return void O(o,n)}const w=o.closest('.wio-delete-entry-btn');if(w.length){const e=w.closest('.wio-entry-item'),o=e.data('book-name'),n=e.data('uid');return void A(o,n)}const p=o.closest('.wio-create-regex-btn');if(p.length){const e=p.data('scope');return void D(e)}const u=o.closest('.wio-edit-regex-btn');if(u.length){const e=u.closest('.wio-regex-item').data('id');return void W(e)}const h=o.closest('.wio-delete-regex-btn');if(h.length){const e=h.closest('.wio-regex-item').data('id');return void P(e)}}),c.on('change',async e=>{const o=$(e.target);if(o.is('.wio-global-book-toggle')){const e=o.closest('.wio-book-group').data('book-name'),n=o.prop('checked');return void await(async(e,o)=>{const n=await g.getLorebookSettings()||{};n.selected_global_lorebooks||(n.selected_global_lorebooks=[]);const t=new Set(n.selected_global_lorebooks);if(o?t.add(e):t.delete(e),n.selected_global_lorebooks=Array.from(t),null!==await g.setLorebookSettings(n)){const n=f(),t=n.allLorebooks.find(o=>o.name===e);t&&(t.enabled=o,k(e=>({...e,allLorebooks:[...n.allLorebooks]})))}})(e,n)}if(o.is('.wio-entry-toggle')){const e=o.closest('.wio-entry-item'),n=e.data('book-name'),t=e.data('uid'),r=o.prop('checked');return void await v(n,t,{enabled:r})}if(o.is('.wio-regex-toggle')){const e=o.closest('.wio-regex-item').data('id'),n=o.prop('checked');return void await L(e,{enabled:n})}}),c.on('input',`#${n}`,e=>{(e=>{k(o=>({...o,searchQuery:e}))})($(e.target).val())})},B=async e=>{try{const o=await c({type:'prompt',title:'重命名世界书',text:`为 "${e}" 输入新的名称:`,value:e});'string'==typeof o&&o.trim()&&o!==e&&await(async(e,o)=>{const n=await g.getLorebookEntries(e);if(null===n)throw new Error(`Failed to get entries for lorebook: ${e}`);if(null===await g.createLorebook(o))throw new Error(`Failed to create new lorebook: ${o}`);if(n.length>0&&null===await g.setLorebookEntries(o,n))throw await g.deleteLorebook(o),new Error(`Failed to set entries for new lorebook: ${o}`);null===await g.deleteLorebook(e)&&console.warn(`Failed to delete old lorebook "${e}" after renaming.`),await x()})(e,o.trim())}catch(e){console.log('Rename operation cancelled.')}},T=async e=>{try{await c({type:'confirm',title:'确认删除',text:`你确定要永久删除世界书 "${e}" 吗？此操作无法撤销。`})&&await(async e=>{null!==await g.deleteLorebook(e)&&await x()})(e)}catch(e){console.log('Delete operation cancelled.')}},M=async e=>{try{const o={...await d({entry:{keys:[],content:'',comment:''},bookName:e,isCreating:!0}),enabled:!0};await(async(e,o)=>{const n=await g.createLorebookEntries(e,[o]);if(null!==n&&n.length>0){const o=f(),t=n[0],r=o.lorebookEntries.get(e)||[];o.lorebookEntries.set(e,[...r,t]),k(e=>({...e,lorebookEntries:new Map(o.lorebookEntries)}))}})(e,o)}catch(e){console.log('Create entry operation cancelled.')}},O=async(e,o)=>{const n=f(),t=n.lorebookEntries.get(e)?.find(e=>e.uid===o);if(t)try{const n=await d({entry:{...t},bookName:e,isCreating:!1}),r={comment:n.comment,keys:n.keys,content:n.content};await v(e,o,r)}catch(e){console.log('Edit entry operation cancelled.')}else console.error(`Entry with UID ${o} not found in book ${e}.`)},A=async(e,o)=>{try{await c({type:'confirm',title:'确认删除',text:'你确定要永久删除这个条目吗？'})&&await(async(e,o)=>{if(null!==await g.deleteLorebookEntries(e,[o])){const n=f(),t=(n.lorebookEntries.get(e)||[]).filter(e=>e.uid!==o);n.lorebookEntries.set(e,t),k(e=>({...e,lorebookEntries:new Map(n.lorebookEntries)}))}})(e,o)}catch(e){console.log('Delete entry operation cancelled.')}},j=async()=>{try{const e=await c({type:'prompt',title:'创建新世界书',text:'请输入新世界书的名称:',value:'New-Lorebook'});'string'==typeof e&&e.trim()&&await(async e=>{null!==await g.createLorebook(e)&&await x()})(e.trim())}catch(e){console.log('Create lorebook operation cancelled.')}},D=async e=>{try{const o=await b({regex:{script_name:'新正则',find_regex:'',replace_string:''},isCreating:!0});await(async e=>{const o=f(),n=[...o.regexes.global.filter(e=>'card'!==e.source),...o.regexes.character.filter(e=>'card'!==e.source)],t={id:`ui-${Date.now()}`,enabled:!0,source:'ui',...e};await E([...n,t])})({...o,scope:e})}catch(e){console.log('Create regex operation cancelled.')}},W=async e=>{const o=f(),n=[...o.regexes.global,...o.regexes.character].find(o=>o.id===e);if(n)if('card'!==n.source)try{const o=await b({regex:{...n},isCreating:!1});await L(e,o)}catch(e){console.log('Edit regex operation cancelled.')}else await c({type:'alert',title:'操作无效',text:'无法编辑来自角色卡的正则表达式。'});else console.error(`Regex with ID ${e} not found.`)},P=async e=>{const o=f(),n=[...o.regexes.global,...o.regexes.character].find(o=>o.id===e);if(n&&'card'===n.source)await c({type:'alert',title:'操作无效',text:'无法删除来自角色卡的正则表达式。'});else try{await c({type:'confirm',title:'确认删除',text:'你确定要永久删除这个正则表达式吗？'})&&await(async e=>{const o=f(),n=[...o.regexes.global.filter(e=>'card'!==e.source),...o.regexes.character.filter(e=>'card'!==e.source)].filter(o=>o.id!==e);await E(n)})(e)}catch(e){console.log('Delete regex operation cancelled.')}},R=async()=>{const e=document.getElementById('wio-bulk-search-input'),o=document.getElementById('wio-bulk-replace-input');if(!e||!o)return void console.error('Bulk replace inputs not found');const n=e.value.trim(),t=o.value.trim();if(n)try{if(await c({type:'confirm',title:'确认批量替换',text:`你确定要将所有世界书条目中的 "${n}" 替换为 "${t}" 吗？\n\n此操作将影响所有匹配的条目名称、关键词和内容。`})){const r=await(async(e,o)=>{if(!e.trim())throw new Error('搜索词不能为空');const n=f();let t=0;const r=new Map,a=new RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,'\\$&'),'gi');for(const[e,i]of n.lorebookEntries.entries()){const n=[];let l=!1;for(const e of i){let r=!1;const i={...e};if(i.comment){const e=i.comment.replace(a,o);e!==i.comment&&(i.comment=e,r=!0)}if(i.keys&&i.keys.length>0){const e=JSON.stringify(i.keys);i.keys=i.keys.map(e=>e.replace(a,o)),JSON.stringify(i.keys)!==e&&(r=!0)}if(i.content){const e=i.content.replace(a,o);e!==i.content&&(i.content=e,r=!0)}r&&(t++,l=!0),n.push(i)}l&&r.set(e,n)}const i=Array.from(r.entries()).map(async([e,o])=>null!==await g.setLorebookEntries(e,o)?()=>n.lorebookEntries.set(e,o):null);return(await Promise.all(i)).filter(Boolean).forEach(e=>e()),r.size>0&&k(e=>({...e,lorebookEntries:new Map(n.lorebookEntries)})),t})(n,t);await c({type:'alert',title:'替换完成',text:`成功替换了 ${r} 个条目中的内容。`}),e.value='',o.value=''}}catch(e){const o=e instanceof Error?e.message:'未知错误';await c({type:'alert',title:'替换失败',text:`批量替换操作失败: ${o}`})}else await c({type:'alert',title:'输入错误',text:'请输入要搜索的内容。'})},F=async()=>{const e=f().selectedItems.size;if(0!==e)try{if(await c({type:'confirm',title:'确认批量删除',text:`你确定要永久删除选中的 ${e} 个条目吗？此操作无法撤销。`})){const e=await(async()=>{const e=f(),o=Array.from(e.selectedItems);if(0===o.length)throw new Error('没有选中任何条目');let n=0;const t=new Map;for(const e of o){const[o,n]=e.split('/');o&&n&&(t.has(o)||t.set(o,[]),t.get(o)?.push(n))}const r=Array.from(t.entries()).map(async([e,o])=>null!==await g.deleteLorebookEntries(e,o)?{bookName:e,uidsToDelete:o,count:o.length}:null),a=(await Promise.all(r)).filter(Boolean);for(const o of a){if(!o)continue;n+=o.count;const t=(e.lorebookEntries.get(o.bookName)||[]).filter(e=>!o.uidsToDelete.includes(e.uid));e.lorebookEntries.set(o.bookName,t)}return e.selectedItems.clear(),k(o=>({...o,lorebookEntries:new Map(e.lorebookEntries),selectedItems:new Set,multiSelectMode:!1})),n})();await c({type:'alert',title:'删除完成',text:`成功删除了 ${e} 个条目。`})}}catch(e){const o=e instanceof Error?e.message:'未知错误';await c({type:'alert',title:'删除失败',text:`批量删除操作失败: ${o}`})}else await c({type:'alert',title:'没有选中项',text:'请先选择要删除的条目。'})},U=async()=>{if(0!==f().selectedItems.size)try{const e=await(async()=>await C(!0))();await c({type:'alert',title:'启用完成',text:`成功启用了 ${e} 个条目。`})}catch(e){const o=e instanceof Error?e.message:'未知错误';await c({type:'alert',title:'启用失败',text:`批量启用操作失败: ${o}`})}else await c({type:'alert',title:'没有选中项',text:'请先选择要启用的条目。'})},N=async()=>{if(0!==f().selectedItems.size)try{const e=await(async()=>await C(!1))();await c({type:'alert',title:'禁用完成',text:`成功禁用了 ${e} 个条目。`})}catch(e){const o=e instanceof Error?e.message:'未知错误';await c({type:'alert',title:'禁用失败',text:`批量禁用操作失败: ${o}`})}else await c({type:'alert',title:'没有选中项',text:'请先选择要禁用的条目。'})},Q=(e,o,n,t=!0,r)=>{const a=void 0!==r?r:o.lorebookEntries.get(e.name)||[],i=o.lorebookEntries.get(e.name)?.length||0,c=o.lorebookUsage.get(e.name)||[],d=o.collapsedBooks.has(e.name),b=n&&a.length!==i?`${a.length} / ${i}`:`${i}`,w=d?'▶':'▼';return`\n        <div class="wio-book-group" data-book-name="${l(e.name)}">\n            <div class="wio-book-header">\n                <button class="wio-collapse-toggle" title="${d?'展开':'折叠'}" aria-label="${d?'展开':'折叠'} ${l(e.name)}">${w}</button>\n                ${t?`<input type="checkbox" ${e.enabled?'checked':''} class="wio-global-book-toggle" aria-label="启用 ${l(e.name)}">`:''}\n                <h4>${s(e.name,n)} <span class="wio-entry-count">(${b})</span></h4>\n                ${c.length>0?`<span class="wio-usage-pill" title="被 ${c.join(', ')} 使用" aria-label="被 ${c.length} 个角色使用">${c.length}</span>`:''}\n                <div class="wio-item-controls">\n                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${l(e.name)}"><i class="fa-solid fa-pencil"></i></button>\n                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${l(e.name)}"><i class="fa-solid fa-trash-can"></i></button>\n                </div>\n            </div>\n            ${d?'':`<div class="wio-entry-list">\n                ${a.map(t=>H(t,e.name,n,o)).join('')}\n                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${l(e.name)}" aria-label="在 ${l(e.name)} 中新建条目">+ 新建条目</button></div>\n            </div>`}\n        </div>\n    `},H=(e,o,n,t)=>{if(!e)return'';const r=e.comment||'(未命名条目)',a=`${o}/${e.uid}`,i=t.selectedItems.has(a);return`\n        <div class="wio-entry-item" data-uid="${e.uid}" data-book-name="${l(o)}" data-item-id="${l(a)}">\n            <div class="wio-entry-main">\n                ${t.multiSelectMode?`<input type="checkbox" ${i?'checked':''} class="wio-multi-select-checkbox" aria-label="选择 ${l(r)}">`:''}\n                <input type="checkbox" ${e.enabled?'checked':''} class="wio-entry-toggle" aria-label="启用 ${l(r)}">\n                <span class="wio-entry-name">${s(r,n)}</span>\n                <span class="wio-entry-keys">${s((e.keys||[]).join(', '),n)}</span>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${l(r)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${l(r)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `},q=(e,o,n,t)=>{let r=`<div class="wio-regex-group" data-scope="${t}"><h3>${n} (${e.length})</h3>`;0!==e.length||'character'!==t||f().character?0===e.length&&(r+='<p class="wio-info-text">没有找到正则。</p>'):r+='<p class="wio-info-text">没有加载角色，无法显示角色正则。</p>';const a=o?e.filter(e=>(e.script_name||'').toLowerCase().includes(o)||(e.find_regex||'').toLowerCase().includes(o)||(e.replace_string||'').toLowerCase().includes(o)):e;return a.length>0?(r+=`<div class="wio-regex-list" data-scope="${t}">`,r+=a.map(e=>((e,o)=>{if(!e)return'';const n=e.script_name||'(未命名正则)',t=e.id||`${e.scope}-${btoa(unescape(encodeURIComponent(e.script_name+e.find_regex)))}`,r='card'!==e.source,a=r?'draggable="true"':'';return`\n        <div class="wio-regex-item ${r?'wio-regex-draggable':''}" data-id="${l(t)}" data-scope="${e.scope}" ${a}>\n            <div class="wio-regex-main">\n                ${r?'<i class="fa-solid fa-grip-vertical wio-drag-handle" aria-label="拖拽排序" title="拖拽调整顺序"></i>':''}\n                <input type="checkbox" ${e.enabled?'checked':''} class="wio-regex-toggle" aria-label="启用 ${l(n)}">\n                <span class="wio-regex-name">${s(n,o)} ${'card'===e.source?'<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>':''}</span>\n                <code class="wio-regex-find" title="查找: ${l(e.find_regex)}">${s(e.find_regex,o)}</code>\n                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>\n                <code class="wio-regex-replace" title="替换为: ${l(e.replace_string)}">${s(e.replace_string,o)}</code>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${l(n)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${l(n)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `})(e,o)).join(''),r+='</div>'):o&&(r+=`<p class="wio-info-text">没有找到与 "${l(o)}" 匹配的正则。</p>`),('global'===t||'character'===t&&f().character)&&(r+=`<div class="wio-regex-actions"><button class="wio-create-regex-btn" data-scope="${t}">+ 新建正则</button></div>`),r+'</div>'};let J,V;const Y=()=>{if(!J)return;const o=f(),i=V(`#${e}`,J);if(!i.length)return;i.find('.wio-tab-btn').removeClass('active'),i.find(`.wio-tab-btn[data-tab-id="${o.activeTab}"]`).addClass('active'),(e=>{if(!J)return;const o=['global-lore','char-lore','chat-lore'].includes(e.activeTab),n=e.selectedItems.size>0,t=V('#wio-bulk-search-input',J),i=V('#wio-bulk-replace-input',J),l=V('#wio-bulk-replace-btn',J);o&&!e.multiSelectMode?(t.show(),i.show(),l.show()):(t.hide(),i.hide(),l.hide());const s=V('#wio-multi-select-btn',J);o?(s.show(),s.toggleClass('active',e.multiSelectMode)):s.hide();const c=V('#wio-bulk-delete-btn',J),d=V('#wio-bulk-enable-btn',J),b=V('#wio-bulk-disable-btn',J);e.multiSelectMode&&o?(c.show(),d.show(),b.show(),c.prop('disabled',!n),d.prop('disabled',!n),b.prop('disabled',!n)):(c.hide(),d.hide(),b.hide());const w=V(`#${a}`,J),g=V('#wio-collapse-all-btn',J),p=V('#wio-expand-all-btn',J),u=['char-lore','chat-lore'].includes(e.activeTab);o?(u?w.show():w.hide(),g.show(),p.show()):(w.hide(),g.hide(),p.hide());const h=V(`#${r}`,J);['char-lore','chat-lore','char-regex'].includes(e.activeTab)?h.show():h.hide()})(o);const s=i.find('.wio-main-content');if(o.isLoading)return void s.html('<p class="wio-info-text">正在加载数据...</p>');if(o.loadError)return void s.html(`\n      <div class="wio-error-container">\n        <p class="wio-error-text">${o.loadError}</p>\n        <button id="${t}-retry" class="wio-retry-btn">重试</button>\n      </div>\n    `);if(!o.isDataLoaded)return void s.html('<p class="wio-info-text">点击刷新按钮加载数据</p>');const c=o.searchQuery.toLowerCase(),d=V(`#${n}`,J);switch(d.val()!==o.searchQuery&&d.val(o.searchQuery),o.activeTab){case'global-lore':s.html(((e,o)=>{const n=[...e.allLorebooks].sort((e,o)=>(o.enabled?1:-1)-(e.enabled?1:-1)||e.name.localeCompare(o.name));return 0===n.length?'<p class="wio-info-text">没有找到全局世界书。</p>':o?n.map(n=>{const t=n.name.toLowerCase().includes(o),r=e.lorebookEntries.get(n.name)||[],a=r.filter(e=>(e.comment||'').toLowerCase().includes(o)||(e.keys||[]).join(', ').toLowerCase().includes(o)||(e.content||'').toLowerCase().includes(o));return t||a.length>0?Q(n,e,o,!0,t?r:a):''}).join('')||`<p class="wio-info-text">没有找到与 "${l(o)}" 匹配的结果。</p>`:n.map(n=>Q(n,e,o)).join('')})(o,c));break;case'char-lore':s.html(((e,o)=>{const n=e.lorebooks.character;return 0===n.length?'<p class="wio-info-text">当前角色没有绑定的世界书。</p>':o?n.map(n=>{const t=e.allLorebooks.find(e=>e.name===n)||{name:n,enabled:!1},r=t.name.toLowerCase().includes(o),a=e.lorebookEntries.get(t.name)||[],i=a.filter(e=>(e.comment||'').toLowerCase().includes(o)||(e.keys||[]).join(', ').toLowerCase().includes(o)||(e.content||'').toLowerCase().includes(o));return r||i.length>0?Q(t,e,o,!1,r?a:i):''}).join('')||`<p class="wio-info-text">没有找到与 "${l(o)}" 匹配的结果。</p>`:n.map(n=>{const t=e.allLorebooks.find(e=>e.name===n)||{name:n,enabled:!1};return Q(t,e,o,!1)}).join('')})(o,c));break;case'chat-lore':s.html(((e,o)=>{const n=e.chatLorebook;if(!n)return'<p class="wio-info-text">当前聊天没有绑定的世界书。</p>';const t=e.allLorebooks.find(e=>e.name===n)||{name:n,enabled:!1};if(!o)return Q(t,e,o,!1);const r=t.name.toLowerCase().includes(o),a=e.lorebookEntries.get(t.name)||[],i=a.filter(e=>(e.comment||'').toLowerCase().includes(o)||(e.keys||[]).join(', ').toLowerCase().includes(o)||(e.content||'').toLowerCase().includes(o));if(r||i.length>0)return Q(t,e,o,!1,r?a:i);return`<p class="wio-info-text">没有找到与 "${l(o)}" 匹配的结果。</p>`})(o,c));break;case'global-regex':s.html(q(o.regexes.global,c,'全局正则','global'));break;case'char-regex':s.html(q(o.regexes.character,c,'角色正则','character'));break;default:s.html(`<p>未知视图: ${o.activeTab}</p>`)}},G=()=>{var e;e=Y,h.push(e)};function K(l){console.log('[WIO] Initializing World Info Optimizer...'),(l=>{if(J=l.document,V=l.jQuery,V(`#${o}`,J).length>0)return void console.log('[WIO] UI already injected.');const s=`\n        :root {\n            /* 紧凑布局 */\n            --wio-font-size-sm: 11px;\n            --wio-font-size-md: 13px;\n            --wio-font-size-lg: 15px;\n            --wio-spacing-xs: 2px;\n            --wio-spacing-sm: 6px;\n            --wio-spacing-md: 10px;\n            --wio-spacing-lg: 14px;\n\n            /* 扁平化风格 */\n            --wio-border-radius: 4px;\n            --wio-shadow: none;\n            \n            /* 暗色主题 */\n            --wio-bg-primary: #1f1f1f;\n            --wio-bg-secondary: #2d2d2d;\n            --wio-bg-tertiary: #3c3c3c;\n            --wio-bg-toolbar: #252525;\n            --wio-text-primary: #e0e0e0;\n            --wio-text-secondary: #9e9e9e;\n            --wio-highlight-color: #29b6f6;\n            --wio-border-color: #424242;\n        }\n\n        /* All WIO styles here... */\n        #${e} {\n            display: none;\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100vw;\n            height: 100vh;\n            z-index: 10000;\n            overflow: hidden;\n        }\n        \n        #${e} .wio-panel-inner {\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            width: 100vw;\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n        }\n\n\n        /* 头部样式 */\n        .wio-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        .wio-header h2 {\n            margin: 0;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-close-btn {\n            background: none;\n            border: none;\n            color: var(--wio-text-secondary);\n            font-size: 24px;\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: color 0.2s ease;\n        }\n        \n        .wio-close-btn:hover {\n            color: var(--wio-text-primary);\n        }\n        \n        /* 选项卡样式 */\n        .wio-tabs {\n            display: flex;\n            background-color: var(--wio-bg-tertiary);\n            overflow-x: auto;\n            white-space: nowrap;\n            flex-shrink: 0;\n            border-bottom: 1px solid var(--wio-border-color);\n            -ms-overflow-style: none;\n            scrollbar-width: none;\n        }\n        \n        .wio-tabs::-webkit-scrollbar {\n            display: none;\n        }\n        \n        .wio-tab-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            border-bottom: 2px solid transparent;\n            flex-shrink: 0;\n            font-size: var(--wio-font-size-md);\n            transition: all 0.2s ease;\n            outline: none;\n        }\n        \n        .wio-tab-btn.active {\n            color: var(--wio-highlight-color);\n            border-bottom-color: var(--wio-highlight-color);\n            background-color: rgba(41, 182, 246, 0.15);\n        }\n        \n        .wio-tab-btn:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n        \n        /* 工具栏样式 */\n        .wio-toolbar {\n            padding: var(--wio-spacing-md);\n            display: flex;\n            gap: var(--wio-spacing-md);\n            background-color: var(--wio-bg-toolbar);\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        #${n} {\n            flex-grow: 1;\n            padding: var(--wio-spacing-md);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary); /* Darker background */\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-md);\n            outline: none;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        \n        #${n}::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        #${n}:focus {\n            border-color: var(--wio-highlight-color);\n            box-shadow: none;\n        }\n        \n        .wio-toolbar button {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: var(--wio-spacing-xs);\n            transition: all 0.2s ease;\n            outline: none;\n            min-width: 40px;\n            min-height: 40px;\n        }\n        \n        .wio-toolbar button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-toolbar button:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 主内容区域样式 */\n        .wio-main-content {\n            flex-grow: 1;\n            overflow-y: auto;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        /* 列表样式 */\n        .wio-book-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-book-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n        }\n        \n        .wio-book-header h4 {\n            margin: 0;\n            flex-grow: 1;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-usage-pill {\n            padding: 2px 8px;\n            background-color: var(--wio-highlight-color);\n            color: #1f1f1f;\n            border-radius: 12px;\n            font-size: var(--wio-font-size-sm);\n            font-weight: bold;\n        }\n        \n        .wio-item-controls {\n            display: flex;\n            gap: var(--wio-spacing-xs);\n        }\n        \n        .wio-item-controls button {\n            padding: var(--wio-spacing-xs);\n            background-color: transparent;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: all 0.2s ease;\n        }\n        \n        .wio-item-controls button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-entry-list {\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-entry-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-entry-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-entry-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n        }\n        \n        .wio-entry-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-entry-keys {\n            font-size: var(--wio-font-size-sm);\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            flex-grow: 1;\n            word-break: break-word;\n        }\n        \n        .wio-entry-actions,\n        .wio-regex-actions {\n            padding: var(--wio-spacing-md);\n            text-align: center;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-create-entry-btn,\n        .wio-create-regex-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-highlight-color);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-create-entry-btn:hover,\n        .wio-create-regex-btn:hover {\n            background-color: #0091ea; /* A slightly darker shade of the highlight color */\n        }\n\n        /* 正则表达式样式 */\n        .wio-regex-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-regex-group h3 {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            margin: 0;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-regex-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-regex-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-regex-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n            flex-wrap: wrap;\n        }\n        \n        .wio-regex-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-regex-find,\n        .wio-regex-replace {\n            background-color: var(--wio-bg-primary);\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: var(--wio-font-size-sm);\n            word-break: break-all;\n            border: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-info-text {\n            text-align: center;\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-container {\n            text-align: center;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-text {\n            color: #ff6b6b;\n            margin-bottom: var(--wio-spacing-md);\n            font-weight: bold;\n        }\n\n        .wio-retry-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: #c62828;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-retry-btn:hover {\n            background-color: #b71c1c;\n        }\n        \n        .wio-search-highlight {\n            background-color: rgba(255, 255, 0, 0.3);\n            padding: 0 2px;\n            border-radius: 2px;\n        }\n\n        /* 页脚样式 */\n        .wio-footer {\n            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            font-size: var(--wio-font-size-sm);\n            text-align: right;\n            border-top: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n\n        /* SweetAlert2 输入框样式覆盖 */\n        .swal2-input, .swal2-textarea {\n            background-color: var(--wio-bg-secondary) !important;\n            color: var(--wio-text-primary) !important;\n            border: 1px solid var(--wio-border-color) !important;\n        }\n        \n        .swal2-input:focus, .swal2-textarea:focus {\n            border-color: var(--wio-highlight-color) !important;\n            box-shadow: none !important;\n        }\n\n        /* SweetAlert2 模态框样式覆盖 */\n        .swal2-popup {\n            background-color: var(--wio-bg-primary) !important;\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-title {\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-html-container {\n            color: var(--wio-text-secondary) !important;\n        }\n\n        .swal2-confirm, .swal2-cancel {\n            border-radius: var(--wio-border-radius) !important;\n            transition: background-color 0.2s ease;\n        }\n\n        .swal2-confirm {\n            background-color: var(--wio-highlight-color) !important;\n        }\n        \n        .swal2-cancel {\n            background-color: var(--wio-bg-tertiary) !important;\n        }\n        \n        .swal2-toast {\n             background-color: var(--wio-bg-secondary) !important;\n             color: var(--wio-text-primary) !important;\n        }\n\n        /* 复选框样式优化 */\n        input[type="checkbox"] {\n            transform: scale(1.1);\n            accent-color: var(--wio-highlight-color);\n            margin-right: var(--wio-spacing-sm);\n            background-color: var(--wio-bg-secondary);\n            border: 1px solid var(--wio-border-color);\n            border-radius: 2px;\n            appearance: none;\n            -webkit-appearance: none;\n            width: 16px;\n            height: 16px;\n            cursor: pointer;\n            position: relative;\n            top: 2px;\n        }\n\n        input[type="checkbox"]:checked {\n            background-color: var(--wio-highlight-color);\n            border-color: var(--wio-highlight-color);\n        }\n\n        input[type="checkbox"]:checked::after {\n            content: '✔';\n            position: absolute;\n            color: #1f1f1f;\n            font-size: 12px;\n            top: -1px;\n            left: 2px;\n        }\n\n        /* 折叠按钮样式 */\n        .wio-collapse-toggle {\n            background: none;\n            border: none;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            margin-right: var(--wio-spacing-sm);\n            font-size: 14px;\n            transition: color 0.2s ease;\n            min-width: 20px;\n            text-align: center;\n        }\n\n        .wio-collapse-toggle:hover {\n            color: var(--wio-text-primary);\n        }\n\n        /* 多选复选框样式 */\n        .wio-multi-select-checkbox {\n            margin-right: var(--wio-spacing-sm);\n            border: 2px solid var(--wio-highlight-color) !important;\n        }\n\n        .wio-multi-select-checkbox:checked {\n            background-color: var(--wio-highlight-color) !important;\n        }\n\n        /* 工具栏按钮激活状态 */\n        .wio-toolbar button.active {\n            background-color: var(--wio-highlight-color);\n            color: #ffffff;\n        }\n\n        /* 批量操作输入框样式 */\n        #wio-bulk-search-input,\n        #wio-bulk-replace-input {\n            flex-grow: 1;\n            max-width: 200px;\n            padding: var(--wio-spacing-sm);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary);\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-sm);\n            outline: none;\n            transition: border-color 0.2s ease;\n        }\n\n        #wio-bulk-search-input:focus,\n        #wio-bulk-replace-input:focus {\n            border-color: var(--wio-highlight-color);\n        }\n\n        #wio-bulk-search-input::placeholder,\n        #wio-bulk-replace-input::placeholder {\n            color: var(--wio-text-secondary);\n        }\n\n        /* 拖拽排序样式 */\n        .wio-regex-draggable {\n            cursor: move;\n            transition: all 0.2s ease;\n        }\n\n        .wio-regex-draggable:hover {\n            background-color: var(--wio-bg-tertiary);\n        }\n\n        .wio-drag-handle {\n            color: var(--wio-text-secondary);\n            cursor: grab;\n            margin-right: var(--wio-spacing-sm);\n            font-size: 12px;\n            opacity: 0.6;\n            transition: opacity 0.2s ease;\n        }\n\n        .wio-drag-handle:hover {\n            opacity: 1;\n            color: var(--wio-text-primary);\n        }\n\n        .wio-regex-draggable:active .wio-drag-handle {\n            cursor: grabbing;\n        }\n\n        .wio-regex-item.wio-dragging {\n            opacity: 0.5;\n            transform: rotate(2deg);\n            z-index: 1000;\n        }\n\n        .wio-regex-list {\n            position: relative;\n        }\n\n        .wio-drop-indicator {\n            height: 2px;\n            background-color: var(--wio-highlight-color);\n            margin: 2px 0;\n            border-radius: 1px;\n            opacity: 0;\n            transition: opacity 0.2s ease;\n        }\n\n        .wio-drop-indicator.active {\n            opacity: 1;\n        }\n\n        /* 聚焦样式优化 */\n        :focus-visible {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 滚动条样式 */\n        .wio-main-content::-webkit-scrollbar,\n        .wio-tabs::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-track,\n        .wio-tabs::-webkit-scrollbar-track {\n            background: var(--wio-bg-tertiary);\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb,\n        .wio-tabs::-webkit-scrollbar-thumb {\n            background: var(--wio-border-color);\n            border-radius: 4px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb:hover,\n        .wio-tabs::-webkit-scrollbar-thumb:hover {\n            background: #777;\n        }\n\n        /* 媒体查询：小型设备优化 */\n        @media (max-width: 767px) {\n            :root {\n                --wio-spacing-xs: 2px;\n                --wio-spacing-sm: 6px;\n                --wio-spacing-md: 8px;\n                --wio-spacing-lg: 10px;\n                --wio-font-size-sm: 11px;\n                --wio-font-size-md: 13px;\n                --wio-font-size-lg: 15px;\n            }\n            \n            .wio-header h2 {\n                font-size: var(--wio-font-size-md);\n            }\n            \n            .wio-entry-main {\n                flex-wrap: wrap;\n            }\n            \n            .wio-entry-name {\n                flex-basis: 100%;\n                margin-bottom: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-main {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-name,\n            .wio-regex-find,\n            .wio-regex-replace {\n                width: 100%;\n                box-sizing: border-box;\n            }\n            \n            .wio-toolbar {\n                flex-wrap: wrap;\n                gap: var(--wio-spacing-sm); /* 缩小gap */\n            }\n            \n            #${n} {\n                flex-basis: 100%; /* 确保搜索框始终占满一行 */\n            }\n            \n            .wio-toolbar button {\n                flex-grow: 1; /* 让按钮平均分配剩余空间 */\n                min-width: 44px; /* 保证最小触摸尺寸 */\n            }\n\n            .wio-tabs {\n                position: relative; /* 为伪元素定位 */\n            }\n\n            .wio-tabs::before,\n            .wio-tabs::after {\n                content: '';\n                position: absolute;\n                top: 0;\n                bottom: 0;\n                width: 20px; /* 渐变宽度 */\n                pointer-events: none; /* 允许点击穿透 */\n            }\n\n            .wio-tabs::before {\n                left: 0;\n                background: linear-gradient(to right, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-tabs::after {\n                right: 0;\n                background: linear-gradient(to left, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-main-content {\n                padding: var(--wio-spacing-md); /* 统一内边距 */\n            }\n\n            .wio-entry-keys {\n                font-size: var(--wio-font-size-sm);\n                white-space: normal; /* 允许长文本换行 */\n                word-break: break-all;\n            }\n            \n            /* 触摸目标优化 */\n            button,\n            input[type="checkbox"] {\n                touch-action: manipulation;\n            }\n        }\n        \n        /* 平板设备优化 */\n        @media (min-width: 768px) and (max-width: 1024px) {\n            #${e} {\n                width: 90%;\n                height: 80%;\n            }\n        }\n\n        /* 高对比度模式支持 */\n        @media (prefers-contrast: high) {\n            :root {\n                --wio-border-color: #fff !important;\n                --wio-bg-primary: #000 !important;\n                --wio-bg-secondary: #333 !important;\n                --wio-bg-tertiary: #222 !important;\n                --wio-bg-toolbar: #444 !important;\n                --wio-text-primary: #fff !important;\n                --wio-text-secondary: #ddd !important;\n                --wio-highlight-color: #ff0 !important;\n            }\n        }\n\n\n        /* 减少动画模式支持 */\n        @media (prefers-reduced-motion: reduce) {\n            * {\n                animation-duration: 0.01ms !important;\n                animation-iteration-count: 1 !important;\n                transition-duration: 0.01ms !important;\n            }\n        }\n    `,c=J.createElement('style');c.innerText=s,J.head.appendChild(c);const d=`\n        <div id="${e}">\n            <div class="wio-panel-inner">\n                <div class="wio-header">\n                    <h2>世界书 & 正则便捷管理 (WIO)</h2>\n                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>\n                </div>\n                <div class="wio-tabs" role="tablist">\n                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>\n                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>\n                </div>\n                <div class="wio-toolbar">\n                    <input type="search" id="${n}" placeholder="搜索..." aria-label="搜索内容">\n                    <input type="text" id="wio-bulk-search-input" placeholder="批量搜索..." aria-label="批量搜索内容" style="display: none;">\n                    <input type="text" id="wio-bulk-replace-input" placeholder="替换为..." aria-label="替换内容" style="display: none;">\n                    <button id="wio-bulk-replace-btn" title="批量替换" aria-label="批量替换" style="display: none;"><i class="fa-solid fa-exchange-alt"></i></button>\n                    <button id="wio-multi-select-btn" title="多选模式" aria-label="切换多选模式"><i class="fa-solid fa-check-square"></i></button>\n                    <button id="wio-bulk-delete-btn" title="批量删除" aria-label="批量删除选中项" style="display: none;"><i class="fa-solid fa-trash"></i></button>\n                    <button id="wio-bulk-enable-btn" title="批量启用" aria-label="批量启用选中项" style="display: none;"><i class="fa-solid fa-eye"></i></button>\n                    <button id="wio-bulk-disable-btn" title="批量禁用" aria-label="批量禁用选中项" style="display: none;"><i class="fa-solid fa-eye-slash"></i></button>\n                    <button id="${a}" title="折叠当前角色" aria-label="折叠当前角色的世界书"><i class="fa-solid fa-user-minus"></i></button>\n                    <button id="wio-collapse-all-btn" title="全部折叠" aria-label="折叠所有世界书"><i class="fa-solid fa-compress"></i></button>\n                    <button id="wio-expand-all-btn" title="全部展开" aria-label="展开所有世界书"><i class="fa-solid fa-expand"></i></button>\n                    <button id="${r}" title="刷新角色数据" aria-label="刷新角色数据"><i class="fa-solid fa-user-sync"></i></button>\n                    <button id="${t}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>\n                    <button id="${i}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>\n                </div>\n                <div class="wio-main-content" role="main" id="tab-content-container"></div>\n                <div class="wio-footer">\n                    <span>WIO v3.0 (Refactored)</span>\n                </div>\n            </div>\n        </div>\n    `;V('body',J).append(d);const b=`\n        <div id="${o}" class="list-group-item">\n            <img src="https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png" style="width: 24px; height: 24px; margin-right: 10px;">\n            <span>世界书 & 正则便捷管理 (WIO)</span>\n        </div>\n    `;V('#extensionsMenu',J).append(b),console.log('[WIO] UI Injected successfully.')})(l),G(),S(l),console.log('[WIO] World Info Optimizer initialized successfully.')}console.log('[WIO Script] Execution started.'),function(e){const o='#extensionsMenu';let n=0;console.log('[WIO] Starting readiness check...');const t=setInterval(()=>{const r=window.parent;if(!r)return void n++;const a=null!==r.document.querySelector(o),i=r.TavernHelper&&'function'==typeof r.TavernHelper.getCharData&&r.jQuery;if(a&&i){clearInterval(t),console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');try{e(r)}catch(e){console.error('[WIO] FATAL: Error during main callback execution.',e)}}else n++,n>100&&(clearInterval(t),console.error('[WIO] FATAL: Readiness check timed out.'),a||console.error(`[WIO] -> Failure: DOM element "${o}" not found.`),i||console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!r.TavernHelper}, jQuery: ${!!r.jQuery}`))},200)}(K);