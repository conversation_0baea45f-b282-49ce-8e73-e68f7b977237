<head><script type="module">var e={88:(e,t)=>{t.A=(e,t)=>{const o=e.__vccOpts||e;for(const[e,n]of t)o[e]=n;return o}},363:()=>{$(()=>{toastr.success('你已经成功加载界面!','恭喜你!')}),$(window).on('pagehide',()=>{toastr.info('你已经卸载界面!','再见!')})}},t={};function o(n){var s=t[n];if(void 0!==s)return s.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,o),r.exports}o(363);const n=Vue,s=VueRouter;var r=o(88);const a={},c=(0,r.A)(a,[['render',function(e,t){const o=(0,n.resolveComponent)('RouterView');return(0,n.openBlock)(),(0,n.createBlock)(o)}]]),l={class:'message-content font-bold underline'},p=(0,n.defineComponent)({__name:'日记',setup(e){const t=(0,n.ref)('');return(0,n.onMounted)(()=>{!function(){const e=substitudeMacros('{{char}}'),o=getCurrentMessageId(),n=getChatMessages(o)[0],s=`${e}: ${n.message.match(/\[查看日记[:：]\s*(.+)\]/)?.[1]??''}`;t.value=s}()}),(e,o)=>((0,n.openBlock)(),(0,n.createElementBlock)('div',{class:'clickdiv',tabindex:'1',onClick:o[0]||(o[0]=t=>e.$router.push('/选择框'))},[(0,n.createElementVNode)('span',l,(0,n.toDisplayString)(t.value),1)]))}}),i={class:'roleplay_options'},u={class:'roleplay_options_back'},d=['onClick'],m={class:'roleplay_options_title'},g={class:'roleplay_options_content'},v=(0,n.defineComponent)({__name:'选择框',setup(e){const t=(0,n.ref)([]);return(0,n.onMounted)(()=>{t.value=function(){const e=getChatMessages(getCurrentMessageId())[0];return[...(e.message.match(/<roleplay_options>(.*?)<\/roleplay_options>/s)?.[1]??'').matchAll(/(.+?)[:：]\s*(.+)/gm)].map(e=>({title:e[1],content:e[2].replace(/^\$\{(.+)\}$/,'$1').replace(/^「(.+)」$/,'$1')}))}()}),(e,o)=>((0,n.openBlock)(),(0,n.createElementBlock)('div',i,[(0,n.createElementVNode)('div',u,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(t.value,e=>((0,n.openBlock)(),(0,n.createElementBlock)('div',{class:'roleplay_options_item',tabindex:'1',onClick:t=>async function(e){await createChatMessages([{role:'user',message:e.content}]),triggerSlash('/trigger')}(e)},[(0,n.createElementVNode)('span',m,[(0,n.createElementVNode)('strong',null,(0,n.toDisplayString)(e.title),1)]),o[0]||(o[0]=(0,n.createElementVNode)('hr',{class:'roleplay_options_hr'},null,-1)),(0,n.createElementVNode)('span',g,(0,n.toDisplayString)(e.content),1)],8,d))),256))])]))}}),h=(0,r.A)(v,[['__scopeId','data-v-52d6ba0e']]),k=(0,s.createRouter)({history:(0,s.createMemoryHistory)(),routes:[{path:'/日记',component:p},{path:'/选择框',component:h}]});k.replace('/日记'),$(()=>{(0,n.createApp)(c).use(k).mount('#app')});</script><style>body{display:flex;justify-content:center;align-items:center;min-height:100px;margin:0;padding:0;font-family:"Microsoft YaHei",Arial,sans-serif}.message-content{font-size:14px;line-height:1.4;letter-spacing:.7px;color:#7d6b6e;font-weight:bold;text-shadow:0px 1px 1px hsla(0,0%,100%,.5)}.clickdiv{position:relative;margin:10px;max-width:150px;padding:.8em 1.2em;background:linear-gradient(135deg,rgba(255,235,240,0.62),rgba(255,202,215,0.62));border-radius:16px;cursor:pointer;outline:none;border:1px solid hsla(0,0%,100%,.7);display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px rgba(145,125,138,.15),0 0 0 1px hsla(0,0%,100%,.4) inset,0 -3px 3px hsla(0,0%,100%,.25) inset;backdrop-filter:blur(13px);-webkit-backdrop-filter:blur(13px);transition:all .3s ease;overflow:hidden}.clickdiv:after{content:"";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,0.2) 0%,rgba(255,255,255,0) 65%);opacity:.4;pointer-events:none}.clickdiv:hover{transform:translateY(-2px);box-shadow:0 6px 15px rgba(145,125,138,.2),0 0 0 1px hsla(0,0%,100%,.5) inset,0 -3px 3px hsla(0,0%,100%,.35) inset;background:linear-gradient(135deg,rgba(255,235,240,0.7),rgba(255,202,215,0.7))}.clickdiv:active{transform:translateY(0);box-shadow:0 2px 8px rgba(145,125,138,.15),0 0 0 1px hsla(0,0%,100%,.4) inset}@media (max-width:999px){.clickdiv{padding:.7em 1em;font-size:13px;max-width:240px}}
.roleplay_options_back[data-v-52d6ba0e]{background:linear-gradient(160deg,rgba(45,45,45,0.75),rgba(35,35,35,0.85));border-radius:14px;box-shadow:0 10px 28px rgba(0,0,0,.15),0 3px 10px rgba(0,0,0,.12);padding:16px 18px;display:flex;flex-direction:column;gap:10px;border:1px solid hsla(0,0%,100%,.06);max-width:100%;margin:20px 0}.roleplay_options_title[data-v-52d6ba0e]{font-size:.94em;font-weight:600;color:#f0f0f0;padding-right:12px;letter-spacing:.02em;text-align:left;margin-bottom:4px}.roleplay_options_content[data-v-52d6ba0e]{font-size:.94em;line-height:1.55;color:#c6c6c6;font-weight:normal;transition:color .25s ease;text-align:left;flex:1;letter-spacing:.015em;overflow-wrap:anywhere}.roleplay_options_content:not(:empty):not(.short-content)~.roleplay_options_title[data-v-52d6ba0e]{width:100%;margin-bottom:8px;border-bottom:1px solid hsla(0,0%,100%,.08);padding-bottom:6px}.roleplay_options_content.short-content[data-v-52d6ba0e]{flex:1}.roleplay_options_hr[data-v-52d6ba0e]{display:none}.roleplay_options_item[data-v-52d6ba0e]{position:relative;background:rgba(50,50,50,.65);border-radius:10px;box-shadow:0 4px 12px rgba(0,0,0,.1);padding:14px 16px;cursor:pointer;border:1px solid hsla(0,0%,100%,.04);transition:all .25s cubic-bezier(0.25,0.8,0.25,1);overflow:hidden;display:flex;flex-wrap:wrap;align-items:flex-start;z-index:1;margin:2px 0;color:#d8d8d8;font-weight:400;line-height:1.5}.roleplay_options_item[data-v-52d6ba0e]::after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(90,90,90,0.06) 0%,transparent 70%);opacity:0;transition:opacity .3s ease;z-index:-1}.roleplay_options_item[data-v-52d6ba0e]:before{content:"";position:absolute;top:0;left:0;width:3px;height:100%;background:linear-gradient(to bottom,rgba(160,160,160,0.6),rgba(180,180,180,0.3));transform:scaleY(0);transform-origin:top;transition:transform .3s cubic-bezier(0.4,0,0.2,1)}.roleplay_options_item[data-v-52d6ba0e]:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,.15);background:rgba(58,58,58,.75);border-color:rgba(200,200,200,.12)}.roleplay_options_item[data-v-52d6ba0e]:hover::after{opacity:1}.roleplay_options_item:hover .roleplay_options_content[data-v-52d6ba0e]{color:#e2e2e2}.roleplay_options_item[data-v-52d6ba0e]:active{transform:translateY(-1px);box-shadow:0 3px 8px rgba(0,0,0,.12)}.roleplay_options_item[data-v-52d6ba0e]:hover:before{transform:scaleY(1)}@media (max-width:768px){.roleplay_options_back[data-v-52d6ba0e]{padding:14px;gap:8px}.roleplay_options_item[data-v-52d6ba0e]{padding:12px 14px}.roleplay_options_title[data-v-52d6ba0e]{font-size:.9em;padding-right:10px}.roleplay_options_content[data-v-52d6ba0e]{font-size:.9em;line-height:1.5}}@media (prefers-reduced-motion:reduce){.roleplay_options_item[data-v-52d6ba0e]{transition:none}.roleplay_options_item[data-v-52d6ba0e]::before,.roleplay_options_item[data-v-52d6ba0e]::after{transition:none}.roleplay_options_content[data-v-52d6ba0e],.roleplay_options_title[data-v-52d6ba0e]{transition:none}}
</style></head><body><div id="app"></div></body>