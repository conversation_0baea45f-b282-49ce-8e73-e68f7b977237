// src/world_info_optimizer/core.ts

import { TavernAPI } from './api';
import { getState, setAllData, updateState } from './store';
import { LorebookEntry, TavernRegex } from './types';

/**
 * 加载所有初始数据。
 * 这是应用启动时调用的主要数据获取函数。
 */
export const loadAllData = async () => {
  const currentState = getState();

  // 防止并发加载
  if (currentState.isLoading) {
    console.log('[WIO Core] Data loading already in progress, skipping...');
    return;
  }

  // 开始加载
  updateState(s => ({
    ...s,
    isDataLoaded: false,
    isLoading: true,
    loadError: null,
  }));

  try {
    const context = TavernAPI.getContext();
    const { characters: allCharacters, characterId, chatId } = context;
    const hasActiveCharacter = characterId !== undefined && characterId !== null;
    const hasActiveChat = chatId !== undefined && chatId !== null;

    // --- 并行获取基础数据 ---
    const results = await Promise.allSettled([
      TavernAPI.getRegexes(),
      TavernAPI.getLorebookSettings(),
      TavernAPI.getLorebooks(),
      hasActiveCharacter ? TavernAPI.getCharData() : Promise.resolve(null),
      hasActiveCharacter ? TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),
      hasActiveChat ? TavernAPI.getChatLorebook() : Promise.resolve(null),
    ]);

    // --- 安全地从Promise结果中提取数据 ---
    const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
    const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
    const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
    const charData = results[3].status === 'fulfilled' ? results[3].value : null;
    const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : null;
    const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;

    // --- 数据处理 ---

    // 1. 正则表达式处理
    const globalRegexes = (allUIRegexes || []).filter(r => r.scope === 'global');
    const characterRegexes = processCharacterRegexes(allUIRegexes, charData);

    // 2. 世界书文件和启用状态
    const enabledGlobalBooks = new Set(globalSettings?.selected_global_lorebooks || []);
    const allLorebooks = (allBookFileNames || []).map(name => ({
      name: name,
      enabled: enabledGlobalBooks.has(name),
    }));

    // 3. 当前角色关联的世界书
    const charBookSet = new Set<string>();
    if (charLinkedBooks?.primary) charBookSet.add(charLinkedBooks.primary);
    if (charLinkedBooks?.additional) charLinkedBooks.additional.forEach(b => charBookSet.add(b));
    const characterLorebooks = Array.from(charBookSet);

    // 4. 计算所有角色对世界书的使用情况 (lorebookUsage)
    const lorebookUsage = new Map<string, string[]>();
    const knownBookNames = new Set<string>(allBookFileNames || []);

    if (Array.isArray(allCharacters)) {
      for (const char of allCharacters) {
        if (!char?.name) continue;
        // 注意: getCharLorebooks 是同步还是异步取决于TavernHelper的实现
        // 为保险起见，我们假设它可能返回一个Promise
        const books = await TavernAPI.getCharLorebooks({ name: char.name });
        const charBooks = new Set<string>();
        if (books?.primary) charBooks.add(books.primary);
        if (books?.additional) books.additional.forEach(b => charBooks.add(b));

        charBooks.forEach(bookName => {
          if (!lorebookUsage.has(bookName)) {
            lorebookUsage.set(bookName, []);
          }
          lorebookUsage.get(bookName)?.push(char.name);
          knownBookNames.add(bookName); // 确保所有被使用的书都被加载
        });
      }
    }

    // 5. 汇总所有需要加载条目的世界书
    characterLorebooks.forEach(b => knownBookNames.add(b));
    if (chatLorebook) {
      knownBookNames.add(chatLorebook);
    }

    // 6. 并行加载所有世界书的条目
    const lorebookEntries = new Map<string, LorebookEntry[]>();
    const entryPromises = Array.from(knownBookNames).map(async name => {
      const entries = await TavernAPI.getLorebookEntries(name);
      if (entries) {
        lorebookEntries.set(name, entries);
      }
    });
    await Promise.all(entryPromises);

    // 7. 更新全局状态
    setAllData({
      globalRegexes,
      characterRegexes,
      allLorebooks,
      characterLorebooks,
      chatLorebook: chatLorebook || null,
      lorebookEntries,
      lorebookUsage,
    });
  } catch (error) {
    console.error('[WIO Core] Failed to load all data:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    updateState(s => ({
      ...s,
      isDataLoaded: false, // 确保清除旧数据
      isLoading: false,
      loadError: `数据加载失败: ${errorMessage}`,
    }));
  } finally {
    // 确保加载状态总是被重置
    updateState(s => ({ ...s, isLoading: false }));
  }
};

/**
 * 局部刷新角色数据。
 * 只重新加载角色相关的正则、关联的世界书和聊天世界书，而不会重新加载所有全局世界书和设置。
 * 这是针对角色切换场景的性能优化。
 */
export const refreshCharacterData = async () => {
  const currentState = getState();

  // 防止并发加载
  if (currentState.isLoading) {
    console.log('[WIO Core] Data loading already in progress, skipping character refresh...');
    return;
  }

  // 开始加载
  updateState(s => ({
    ...s,
    isLoading: true,
    loadError: null,
  }));

  try {
    const context = TavernAPI.getContext();
    const { characterId, chatId } = context;
    const hasActiveCharacter = characterId !== undefined && characterId !== null;
    const hasActiveChat = chatId !== undefined && chatId !== null;

    // --- 并行获取角色相关数据 ---
    const results = await Promise.allSettled([
      TavernAPI.getRegexes(), // 需要获取所有正则以提取角色正则
      hasActiveCharacter ? TavernAPI.getCharData() : Promise.resolve(null),
      hasActiveCharacter ? TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),
      hasActiveChat ? TavernAPI.getChatLorebook() : Promise.resolve(null),
    ]);

    // --- 安全地从Promise结果中提取数据 ---
    const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
    const charData = results[1].status === 'fulfilled' ? results[1].value : null;
    const charLinkedBooks = results[2].status === 'fulfilled' ? results[2].value : null;
    const chatLorebook = results[3].status === 'fulfilled' ? results[3].value : null;

    // --- 数据处理 ---

    // 1. 角色正则表达式处理
    const characterRegexes = processCharacterRegexes(allUIRegexes, charData);

    // 2. 当前角色关联的世界书
    const charBookSet = new Set<string>();
    if (charLinkedBooks?.primary) charBookSet.add(charLinkedBooks.primary);
    if (charLinkedBooks?.additional) charLinkedBooks.additional.forEach(b => charBookSet.add(b));
    const characterLorebooks = Array.from(charBookSet);

    // 3. 收集需要加载条目的世界书（只包括角色相关的）
    const booksToLoad = new Set<string>();
    characterLorebooks.forEach(b => booksToLoad.add(b));
    if (chatLorebook) {
      booksToLoad.add(chatLorebook);
    }

    // 4. 并行加载角色相关世界书的条目
    const newLorebookEntries = new Map(currentState.lorebookEntries); // 保留现有的全局世界书条目
    const entryPromises = Array.from(booksToLoad).map(async name => {
      const entries = await TavernAPI.getLorebookEntries(name);
      if (entries) {
        newLorebookEntries.set(name, entries);
      }
    });
    await Promise.all(entryPromises);

    // 5. 更新状态（只更新角色相关的部分）
    updateState(s => ({
      ...s,
      regexes: {
        ...s.regexes,
        character: characterRegexes, // 只更新角色正则
      },
      lorebooks: {
        character: characterLorebooks,
      },
      chatLorebook: chatLorebook || null,
      lorebookEntries: newLorebookEntries,
      isDataLoaded: true,
      isLoading: false,
    }));

    console.log('[WIO Core] Character data refreshed successfully');
  } catch (error) {
    console.error('[WIO Core] Failed to refresh character data:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    updateState(s => ({
      ...s,
      isLoading: false,
      loadError: `角色数据刷新失败: ${errorMessage}`,
    }));
  }
};

/**
 * 处理和合并来自UI和角色卡中的角色特定正则表达式。
 * @param allUIRegexes 从Tavern API获取的所有UI正则
 * @param charData 当前角色的数据
 * @returns 合并和去重后的角色正则表达式数组
 */
function processCharacterRegexes(allUIRegexes: TavernRegex[] | null, charData: any): TavernRegex[] {
  const characterUIRegexes = allUIRegexes?.filter(r => r.scope === 'character') || [];
  let cardRegexes: TavernRegex[] = [];

  if (charData && TavernAPI.Character) {
    try {
      const character = new TavernAPI.Character(charData);
      cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({
        id: r.id || `card-${Date.now()}-${i}`,
        script_name: r.scriptName || '未命名卡内正则',
        find_regex: r.findRegex,
        replace_string: r.replaceString,
        enabled: !r.disabled,
        scope: 'character',
        source: 'card',
      }));
    } catch (e) {
      console.warn("[WIO Core] Couldn't parse character card regex scripts:", e);
    }
  }

  // 去重逻辑：UI中的正则优先
  const uiRegexIdentifiers = new Set(
    characterUIRegexes.map(r => `${r.script_name}::${r.find_regex}::${r.replace_string}`),
  );
  const uniqueCardRegexes = cardRegexes.filter(r => {
    const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
    return !uiRegexIdentifiers.has(identifier);
  });

  return [...characterUIRegexes, ...uniqueCardRegexes];
}

// --- Lorebook CRUD Operations ---

/**
 * 创建一个新的空世界书。
 * @param bookName 新世界书的名称
 */
export const createLorebook = async (bookName: string) => {
  const result = await TavernAPI.createLorebook(bookName);
  if (result !== null) {
    await loadAllData(); // 重新加载所有数据以确保状态同步
  }
};

/**
 * 重命名一个世界书。
 * @param oldName 旧名称
 * @param newName 新名称
 */
export const renameLorebook = async (oldName: string, newName: string) => {
  // 1. 获取旧书的所有条目
  const entries = await TavernAPI.getLorebookEntries(oldName);
  if (entries === null) {
    throw new Error(`Failed to get entries for lorebook: ${oldName}`);
  }

  // 2. 创建一个新书
  const createResult = await TavernAPI.createLorebook(newName);
  if (createResult === null) {
    throw new Error(`Failed to create new lorebook: ${newName}`);
  }

  // 3. 将条目复制到新书
  if (entries.length > 0) {
    const setResult = await TavernAPI.setLorebookEntries(newName, entries);
    if (setResult === null) {
      // 清理：如果条目设置失败，删除新建的书
      await TavernAPI.deleteLorebook(newName);
      throw new Error(`Failed to set entries for new lorebook: ${newName}`);
    }
  }

  // 4. 删除旧书
  const deleteResult = await TavernAPI.deleteLorebook(oldName);
  if (deleteResult === null) {
    // 这不是一个关键错误，但应该记录下来
    console.warn(`Failed to delete old lorebook "${oldName}" after renaming.`);
  }

  // 5. 刷新数据
  await loadAllData();
};

/**
 * 删除一个世界书。
 * @param bookName 要删除的世界书的名称
 */
export const deleteLorebook = async (bookName: string) => {
  const result = await TavernAPI.deleteLorebook(bookName);
  if (result !== null) {
    await loadAllData();
  }
};

// --- Lorebook Entry CRUD Operations ---

/**
 * 在指定的世界书中创建一个新条目。
 * @param bookName 世界书名称
 * @param entryData 要创建的条目的数据
 */
export const createLorebookEntry = async (bookName: string, entryData: LorebookEntry) => {
  // Tavern API需要一个数组
  const result = await TavernAPI.createLorebookEntries(bookName, [entryData]);
  if (result !== null && result.length > 0) {
    const newState = getState();
    const newEntry = result[0]; // API会返回创建后的条目，包含UID
    const currentEntries = newState.lorebookEntries.get(bookName) || [];
    newState.lorebookEntries.set(bookName, [...currentEntries, newEntry]);
    updateState(s => ({ ...s, lorebookEntries: new Map(newState.lorebookEntries) }));
  }
};

/**
 * 更新世界书中的一个现有条目。
 * @param bookName 世界书名称
 * @param uid 要更新的条目的唯一ID
 * @param updates 要应用于条目的部分更新
 */
export const updateLorebookEntry = async (bookName: string, uid: string, updates: Partial<LorebookEntry>) => {
  const state = getState();
  const entries = state.lorebookEntries.get(bookName);
  if (!entries) {
    throw new Error(`[WIO Core] Book not found in state: ${bookName}`);
  }

  let entryUpdated = false;
  const updatedEntries = entries.map(entry => {
    if (entry.uid === uid) {
      entryUpdated = true;
      return { ...entry, ...updates };
    }
    return entry;
  });

  if (entryUpdated) {
    const result = await TavernAPI.setLorebookEntries(bookName, updatedEntries);
    if (result !== null) {
      state.lorebookEntries.set(bookName, updatedEntries);
      updateState(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));
    }
  }
};

/**
 * 从世界书中删除一个条目。
 * @param bookName 世界书名称
 * @param uid 要删除的条目的唯一ID
 */
export const deleteLorebookEntry = async (bookName: string, uid: string) => {
  const result = await TavernAPI.deleteLorebookEntries(bookName, [uid]);
  if (result !== null) {
    const state = getState();
    const entries = state.lorebookEntries.get(bookName) || [];
    const filteredEntries = entries.filter(entry => entry.uid !== uid);
    state.lorebookEntries.set(bookName, filteredEntries);
    updateState(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));
  }
};

// --- Global Settings ---

/**
 * 设置一个全局世界书的启用状态。
 * @param bookName 世界书的名称
 * @param enabled true为启用, false为禁用
 */
export const setGlobalLorebookEnabled = async (bookName: string, enabled: boolean) => {
  const settings = (await TavernAPI.getLorebookSettings()) || {};
  if (!settings.selected_global_lorebooks) {
    settings.selected_global_lorebooks = [];
  }

  const enabledBooks = new Set(settings.selected_global_lorebooks);
  if (enabled) {
    enabledBooks.add(bookName);
  } else {
    enabledBooks.delete(bookName);
  }
  settings.selected_global_lorebooks = Array.from(enabledBooks);

  const result = await TavernAPI.setLorebookSettings(settings);
  if (result !== null) {
    // 更新本地状态以立即反映UI变化
    const state = getState();
    const book = state.allLorebooks.find(b => b.name === bookName);
    if (book) {
      book.enabled = enabled;
      updateState(s => ({ ...s, allLorebooks: [...state.allLorebooks] }));
    }
  }
};

// --- Regex CRUD Operations ---

/**
 * 提交正则表达式列表的更改。
 * @param updatedList 要提交的完整正则表达式列表 (仅限UI来源)
 */
const commitRegexChanges = async (updatedList: TavernRegex[]) => {
  const result = await TavernAPI.replaceRegexes(updatedList);
  if (result !== null) {
    // 成功后，重新加载所有数据以确保与后端完全同步
    // 这是最简单可靠的方式，避免复杂的本地状态管理
    await loadAllData();
  }
};

/**
 * 创建一个新的正则表达式。
 * @param newRegexData 要创建的正则表达式的数据
 */
export const createRegex = async (newRegexData: Partial<TavernRegex>) => {
  const state = getState();
  // 只处理UI来源的正则
  const currentUIRegexes = [
    ...state.regexes.global.filter(r => r.source !== 'card'),
    ...state.regexes.character.filter(r => r.source !== 'card'),
  ];

  const finalNewRegex: TavernRegex = {
    id: `ui-${Date.now()}`,
    enabled: true,
    source: 'ui',
    ...newRegexData,
  } as TavernRegex;

  await commitRegexChanges([...currentUIRegexes, finalNewRegex]);
};

/**
 * 更新一个现有的正则表达式。
 * @param regexId 要更新的正则表达式的ID
 * @param updates 要应用的更新
 */
export const updateRegex = async (regexId: string, updates: Partial<TavernRegex>) => {
  const state = getState();
  const currentUIRegexes = [
    ...state.regexes.global.filter(r => r.source !== 'card'),
    ...state.regexes.character.filter(r => r.source !== 'card'),
  ];

  const updatedList = currentUIRegexes.map(r => (r.id === regexId ? { ...r, ...updates } : r));

  await commitRegexChanges(updatedList);
};

/**
 * 删除一个正则表达式。
 * @param regexId 要删除的正则表达式的ID
 */
export const deleteRegex = async (regexId: string) => {
  const state = getState();
  const currentUIRegexes = [
    ...state.regexes.global.filter(r => r.source !== 'card'),
    ...state.regexes.character.filter(r => r.source !== 'card'),
  ];

  const updatedList = currentUIRegexes.filter(r => r.id !== regexId);

  await commitRegexChanges(updatedList);
};

// --- Bulk Operations ---

/**
 * 执行批量搜索和替换操作。
 * @param searchTerm 要搜索的词语
 * @param replaceTerm 要替换成的词语
 * @returns 返回被修改的条目数量
 */
export const performBulkReplace = async (searchTerm: string, replaceTerm: string): Promise<number> => {
  if (!searchTerm.trim()) {
    throw new Error('搜索词不能为空');
  }

  const state = getState();
  let modifiedCount = 0;
  const modifiedBooks = new Map<string, LorebookEntry[]>();

  // 提前创建正则表达式以提高性能
  const searchRegex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');

  // 遍历所有世界书和条目
  for (const [bookName, entries] of state.lorebookEntries.entries()) {
    const modifiedEntries: LorebookEntry[] = [];
    let bookHasChanges = false;

    for (const entry of entries) {
      let entryModified = false;
      const updatedEntry = { ...entry };

      // 替换条目名称 (comment)
      if (updatedEntry.comment) {
        const newComment = updatedEntry.comment.replace(searchRegex, replaceTerm);
        if (newComment !== updatedEntry.comment) {
          updatedEntry.comment = newComment;
          entryModified = true;
        }
      }

      // 替换关键词
      if (updatedEntry.keys && updatedEntry.keys.length > 0) {
        const originalKeys = JSON.stringify(updatedEntry.keys);
        updatedEntry.keys = updatedEntry.keys.map(key => key.replace(searchRegex, replaceTerm));
        if (JSON.stringify(updatedEntry.keys) !== originalKeys) {
          entryModified = true;
        }
      }

      // 替换内容
      if (updatedEntry.content) {
        const newContent = updatedEntry.content.replace(searchRegex, replaceTerm);
        if (newContent !== updatedEntry.content) {
          updatedEntry.content = newContent;
          entryModified = true;
        }
      }

      if (entryModified) {
        modifiedCount++;
        bookHasChanges = true;
      }

      modifiedEntries.push(updatedEntry);
    }

    if (bookHasChanges) {
      modifiedBooks.set(bookName, modifiedEntries);
    }
  }

  // 并行保存所有修改过的世界书
  const updatePromises = Array.from(modifiedBooks.entries()).map(async ([bookName, entries]) => {
    const result = await TavernAPI.setLorebookEntries(bookName, entries);
    if (result !== null) {
      // 在 Promise 内部准备状态更新，但不直接修改
      return () => state.lorebookEntries.set(bookName, entries);
    }
    return null;
  });

  const stateUpdaters = (await Promise.all(updatePromises)).filter(Boolean);
  stateUpdaters.forEach(update => update!());

  if (modifiedBooks.size > 0) {
    updateState(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));
  }

  return modifiedCount;
};

/**
 * 批量删除选中的条目。
 * @returns 返回删除的条目数量
 */
export const performBulkDelete = async (): Promise<number> => {
  const state = getState();
  const selectedItems = Array.from(state.selectedItems);

  if (selectedItems.length === 0) {
    throw new Error('没有选中任何条目');
  }

  let deletedCount = 0;
  const booksToUpdate = new Map<string, string[]>(); // bookName -> uids to delete

  // 解析选中的条目
  for (const itemId of selectedItems) {
    const [bookName, uid] = itemId.split('/');
    if (bookName && uid) {
      if (!booksToUpdate.has(bookName)) {
        booksToUpdate.set(bookName, []);
      }
      booksToUpdate.get(bookName)?.push(uid);
    }
  }

  // 并行删除每个世界书中的条目
  const deletePromises = Array.from(booksToUpdate.entries()).map(async ([bookName, uidsToDelete]) => {
    const result = await TavernAPI.deleteLorebookEntries(bookName, uidsToDelete);
    if (result !== null) {
      return { bookName, uidsToDelete, count: uidsToDelete.length };
    }
    return null;
  });

  const deleteResults = (await Promise.all(deletePromises)).filter(Boolean);

  for (const result of deleteResults) {
    if (!result) continue;
    deletedCount += result.count;
    // 更新本地状态
    const entries = state.lorebookEntries.get(result.bookName) || [];
    const filteredEntries = entries.filter(entry => !result.uidsToDelete.includes(entry.uid));
    state.lorebookEntries.set(result.bookName, filteredEntries);
  }

  // 清空选中状态
  state.selectedItems.clear();
  updateState(s => ({
    ...s,
    lorebookEntries: new Map(state.lorebookEntries),
    selectedItems: new Set(),
    multiSelectMode: false,
  }));

  return deletedCount;
};

/**
 * 批量启用选中的条目。
 * @returns 返回启用的条目数量
 */
export const performBulkEnable = async (): Promise<number> => {
  return await performBulkToggle(true);
};

/**
 * 批量禁用选中的条目。
 * @returns 返回禁用的条目数量
 */
export const performBulkDisable = async (): Promise<number> => {
  return await performBulkToggle(false);
};

/**
 * 批量切换条目的启用/禁用状态。
 * @param enabled 目标状态
 * @returns 返回修改的条目数量
 */
const performBulkToggle = async (enabled: boolean): Promise<number> => {
  const state = getState();
  const selectedItems = Array.from(state.selectedItems);

  if (selectedItems.length === 0) {
    throw new Error('没有选中任何条目');
  }

  let modifiedCount = 0;
  const booksToUpdate = new Map<string, LorebookEntry[]>();

  // 解析选中的条目并准备更新
  for (const itemId of selectedItems) {
    const [bookName, uid] = itemId.split('/');
    if (bookName && uid) {
      const entries = state.lorebookEntries.get(bookName) || [];
      const updatedEntries = entries.map(entry => {
        if (entry.uid === uid && entry.enabled !== enabled) {
          modifiedCount++;
          return { ...entry, enabled };
        }
        return entry;
      });

      if (updatedEntries.some((entry, index) => entry !== entries[index])) {
        booksToUpdate.set(bookName, updatedEntries);
      }
    }
  }

  // 并行保存所有修改过的世界书
  const updatePromises = Array.from(booksToUpdate.entries()).map(async ([bookName, entries]) => {
    const result = await TavernAPI.setLorebookEntries(bookName, entries);
    if (result !== null) {
      return () => state.lorebookEntries.set(bookName, entries);
    }
    return null;
  });

  const stateUpdaters = (await Promise.all(updatePromises)).filter(Boolean);
  stateUpdaters.forEach(update => update!());

  // 清空选中状态并退出多选模式
  state.selectedItems.clear();
  updateState(s => ({
    ...s,
    lorebookEntries: new Map(state.lorebookEntries),
    selectedItems: new Set(),
    multiSelectMode: false,
  }));

  return modifiedCount;
};
