// src/world_info_optimizer/types.ts

/**
 * 表示一个正则表达式脚本。
 */
export interface TavernRegex {
  id: string;
  script_name: string;
  find_regex: string;
  replace_string: string;
  enabled: boolean;
  scope: 'global' | 'character';
  source?: 'card' | 'ui';
}

/**
 * 表示一个世界书文件。
 */
export interface LorebookFile {
  name: string;
  enabled: boolean; // 仅用于全局世界书
}

/**
 * 表示世界书中的一个条目。
 */
export interface LorebookEntry {
  uid: string;
  keys: string[];
  content: string;
  comment: string; // 条目名称
  enabled: boolean;
  order: number;
  // ... 其他可能的字段
  [key: string]: any;
}

/**
 * 应用程序的全局状态。
 */
export interface AppState {
  regexes: {
    global: TavernRegex[];
    character: TavernRegex[];
  };
  lorebooks: {
    character: string[]; // 角色关联的世界书名称列表
  };
  chatLorebook: string | null;
  allLorebooks: LorebookFile[];
  lorebookEntries: Map<string, LorebookEntry[]>;
  lorebookUsage: Map<string, string[]>; // 世界书 -> 使用该书的角色名称列表
  activeTab: string;
  isDataLoaded: boolean;
  isLoading: boolean;
  loadError: string | null;
  searchFilters: {
    bookName: boolean;
    entryName: boolean;
    keywords: boolean;
    content: boolean;
  };
  searchQuery: string;
  multiSelectMode: boolean;
  selectedItems: Set<string>; // 存储唯一标识符，例如 "bookName/entryUid"
  collapsedBooks: Set<string>; // 存储已折叠的世界书名称
}

/**
 * 模态框的配置选项。
 */
export interface ModalOptions {
  type?: 'alert' | 'confirm' | 'prompt';
  title?: string;
  text?: string;
  placeholder?: string;
  value?: string;
}

/**
 * 条目编辑器模态框的配置选项。
 */
export interface EntryEditorOptions {
  entry: Partial<LorebookEntry>; // 使用 Partial<LorebookEntry> 以支持创建新条目
  bookName: string;
  isCreating: boolean;
}

/**
 * 正则表达式编辑器模态框的配置选项。
 */
export interface RegexEditorOptions {
  regex: Partial<TavernRegex>;
  isCreating: boolean;
}
