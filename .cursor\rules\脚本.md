---
description: 当需要编写脚本时, 你应该参考本文件
---
# 脚本

如果 `src/xxx` 文件夹中仅有 `index.ts` 文件, 则它是一个脚本项目.

脚本以无沙盒 iframe 的形式在酒馆后台运行, 没有自己的界面, 只有代码部分可供编写.

## jquery

脚本中的 jquery 将直接作用于整个酒馆页面而非仅作用于脚本所在的 iframe. 例如 `$('body')` 将选择酒馆网页的 `<body>` 标签, 而不是脚本所在的 iframe 的 `<body>` 标签.

## vue

由于脚本运行在 iframe 中, 当需要在脚本中向酒馆页面挂载 vue 组件时, 你应该使用 `.mount($('#app')[0])` 来选择要挂载的位置, 而不是 `.mount('#app')`. 因为 `.mount('#app')` 将会在 iframe 内查找位置, 而不是在酒馆页面中.

## 按钮

脚本可以在酒馆助手脚本库界面中设置按钮, 用户点击按钮时将会触发对应的事件.

我们可以在代码中这样注册按钮事件:

```typescript
eventOn(getButtonEvent('按钮名'), () => {
  console.log('按钮被点击了');
});
```
