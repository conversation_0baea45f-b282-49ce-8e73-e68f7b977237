# WIO v3 功能补全 - 详细开发计划

本文档基于 `feature-gap-analysis.md`，旨在将功能需求转化为一份详细的、可执行的开发路线图。计划分为三个主要阶段，与功能优先级（高、中、低）相对应。

---

## Phase 1: 核心功能实现 (高优先级)

**`Status: Completed`**

此阶段的目标是补全 v3 版本缺失的核心功能，让其在核心能力上能与 v2.7 版本对齐。

### 1. 批量搜索与替换

**目标**: 实现一个允许用户在所有世界条目中执行搜索和替换操作的界面和后端逻辑。

* **[x] Task 1.1: UI 组件添加**
  * **文件**: [`src/world_info_optimizer/ui.ts`](src/world_info_optimizer/ui.ts)
  * **内容**: 在主工具栏区域 (`wio-toolbar`) 添加两个 `HTMLInputElement`: 一个用于“搜索词”，一个用于“替换词”。再添加一个 `HTMLButtonElement` 作为“全部替换”的触发按钮。

* **[x] Task 1.2: 事件绑定**
  * **文件**: [`src/world_info_optimizer/events.ts`](src/world_info_optimizer/events.ts)
  * **内容**: 为“全部替换”按钮注册 `click` 事件监听器。该处理器应从 UI 中获取搜索词和替换词。

* **[x] Task 1.3: 核心替换逻辑**
  * **文件**: [`src/world_info_optimizer/core.ts`](src/world_info_optimizer/core.ts)
  * **内容**: 创建 `performBulkReplace(searchTerm: string, replaceTerm: string)` 函数。
    * 此函数将遍历 `store` 中的所有世界书 (`lorebooks`) 和条目 (`entries`)。
    * 对每个条目的 `name`, `keywords`, `content` 进行不区分大小写的替换。
    * 收集所有被修改过的条目对象。
    * 调用 `TavernAPI` 的更新函数来批量保存这些条目。

* **[x] Task 1.4: 用户交互与反馈**
  * **文件**: [`src/world_info_optimizer/events.ts`](src/world_info_optimizer/events.ts) (事件处理器内部)
  * **内容**: 在执行替换前，使用 `SweetAlert2` 弹出一个确认框，向用户展示将要替换的内容和受影响的条目数量。
  * 在 `core` 函数执行完毕后，显示一个成功或失败的通知。

### 2. 条目多选与批量操作

**目标**: 引入多选模式，允许用户选择多个条目或世界书进行批量删除、启用/禁用等操作。

* **[x] Task 2.1: 状态管理扩展**
  * **文件**: [`src/world_info_optimizer/store.ts`](src/world_info_optimizer/store.ts)
  * **内容**: 在 `WIOState` 接口和初始 `store` 对象中添加 `multiSelectMode: boolean` (默认为 `false`) 和 `selectedItems: Set<string>` (默认为空的 `Set`)。

* **[x] Task 2.2: UI 切换与操作按钮**
  * **文件**: [`src/world_info_optimizer/ui.ts`](src/world_info_optimizer/ui.ts)
  * **内容**: 在工具栏添加“进入/退出多选模式”按钮。同时，添加“批量删除”、“批量启用”、“批量禁用”等按钮，这些按钮仅在 `multiSelectMode` 为 `true` 时可见。

* **[x] Task 2.3: 渲染逻辑调整**
  * **文件**: [`src/world_info_optimizer/ui/views.ts`](src/world_info_optimizer/ui/views.ts)
  * **内容**: 修改 `renderGlobalLorebookView`。当 `store.multiSelectMode` 为 `true` 时，在每个世界书和条目的名称前渲染一个复选框 (`<input type="checkbox">`)。复选框的 `checked` 状态应与 `store.selectedItems` 中的内容同步。

* **[x] Task 2.4: 事件处理**
  * **文件**: [`src/world_info_optimizer/events.ts`](src/world_info_optimizer/events.ts)
  * **内容**: 实现以下事件处理器：
    * 点击“多选模式”按钮时，切换 `store.multiSelectMode` 的布尔值。
    * 点击复选框时，向 `store.selectedItems` 中添加或删除对应的条目 ID。
    * 点击批量操作按钮时，调用 `core.ts` 中对应的处理函数。

* **[x] Task 2.5: 核心批量处理逻辑**
  * **文件**: [`src/world_info_optimizer/core.ts`](src/world_info_optimizer/core.ts)
  * **内容**: 创建 `performBulkDelete()`, `performBulkEnable()`, `performBulkDisable()` 等函数。这些函数会读取 `store.selectedItems` 集合，并对集合中的每一个条目执行相应的 `TavernAPI` 操作。

### 3. 世界书/条目折叠

**目标**: 允许用户折叠或展开单个或全部世界书，以更好地管理视图。

* **[x] Task 3.1: 状态管理**
  * **文件**: [`src/world_info_optimizer/store.ts`](src/world_info_optimizer/store.ts)
  * **内容**: 在 `WIOState` 中添加 `collapsedBooks: Set<string>` 用于记录已折叠的世界书的 ID。

* **[x] Task 3.2: UI 渲染与交互**
  * **文件**: [`src/world_info_optimizer/ui/views.ts`](src/world_info_optimizer/ui/views.ts)
  * **内容**: 在渲染每个世界书的标题时，添加一个可点击的折叠/展开图标（例如 `▶`/`▼`）。
  * 根据 `store.collapsedBooks` 是否包含当前世界书的 ID，决定是否渲染其下的条目列表 (`wio-entry-list`) 并切换图标状态。

* **[x] Task 3.3: 事件处理**
  * **文件**: [`src/world_info_optimizer/events.ts`](src/world_info_optimizer/events.ts)
  * **内容**: 为每个世界书标题的折叠图标绑定 `click` 事件，用于在 `store.collapsedBooks` 中添加或移除该世界书的 ID。

* **[x] Task 3.4: 全局控制**
  * **文件**: [`src/world_info_optimizer/ui.ts`](src/world_info_optimizer/ui.ts) & [`src/world_info_optimizer/events.ts`](src/world_info_optimizer/events.ts)
  * **内容**: 在工具栏添加“全部折叠”和“全部展开”按钮。它们的事件处理器将分别把所有世界书的 ID 添加到 `store.collapsedBooks` 或清空该 `Set`。

---

## Phase 2: 体验优化 (中优先级)

**`Status: Completed`**

此阶段专注于提升用户操作的流畅度和效率。

* **[x] 功能点 4: 局部刷新角色数据 (`refreshCharacterData`)**
  * **背景**: 此前版本仅支持全局刷新 (`loadAllData`)，在切换角色时效率低下。
  * **任务**:
    * 在 `core.ts` 中实现 `refreshCharacterData()` 函数，该函数仅重新获取与当前角色相关的世界书、正则等数据。
    * 在 `events.ts` 中，监听角色切换事件，并调用此函数。
  * **状态**: 已完成。此优化显著减少了切换角色时不必要的数据加载，提升了应用的响应速度。

* **[x] 功能点 5: 折叠当前角色的世界书 (`collapseCurrentCharacterBooks`)**
  * **背景**: 缺少一个精确控制视图的功能，无法快速折叠与当前角色关联的世界书。
  * **任务**:
    * 在 `ui.ts` 中，确保 “折叠当前角色世界书” 按钮 (`#wio-collapse-current`) 只在 “角色世界书” 和 “聊天世界书” 标签页中可见。
    * 在 `events.ts` 中为该按钮绑定点击事件。
    * 在 `core.ts` 中实现 `collapseCurrentCharacterBooks()` 核心逻辑，找出与当前角色关联的所有世界书，并更新 `store.collapsedBooks` 状态。
  * **状态**: 已完成。原有的 BUG 已修复，功能按预期工作。

* **功能点 6: 搜索范围过滤器**
  * **任务**: 在 `store.ts` 中添加 `searchFilters` 对象。在 `ui.ts` 中创建对应的复选框。在 `events.ts` 中监听变化并更新 `store`。最后，在 `ui/views.ts` 的过滤逻辑中同时考虑 `searchQuery` 和 `searchFilters`。

* **[x] 功能点 7: 正则表达式拖拽排序**
  * **任务**: ~~引入 `Sortable.js` 或类似库~~。使用HTML5原生拖拽API。在 `renderRegexView` 中初始化排序功能。监听排序结束事件，获取新的顺序，并调用 `core.ts` 中的函数通过 `TavernAPI.replaceRegexes` 提交完整的有序列表。
  * **实现状态**: ✅ **已完成** - 使用HTML5原生拖拽API实现，无需外部库依赖
  * **关键特性**:
    * ✅ 只有UI来源的正则可拖拽，卡片正则保持只读
    * ✅ 全局正则和角色正则分别独立排序
    * ✅ 拖拽手柄图标和视觉反馈
    * ✅ 智能拖拽位置检测和数组重排序
    * ✅ 通过 `reorderRegex()` 函数调用 `commitRegexChanges()` 持久化
  * **修复**: 解决了BUGFIX-REPORT.md中标识的阻塞性BUG

* **功能点 8: 操作反馈提示 (Toast)**
  * **任务**: 创建 `ui/notifications.ts` 文件，实现一个非阻塞的 `showToast` 函数。在 `core.ts` 的各个异步操作中（如创建、更新、删除）调用此函数，提供即时的操作反馈。

---

## Phase 3: 细节完善 (低优先级)

此阶段处理一些次要但能提升界面信息清晰度的功能。

* **功能点 9: 世界书使用情况指示**
  * **任务**: 在 `renderGlobalLorebookView` 中，读取已存在于 `state.lorebookUsage` 中的数据，并将其渲染为世界书名称旁边的一个小标签 (Pill)。

* **功能点 10: 卡内正则的只读标识**
  * **任务**: 在 `renderRegexView` 中，检查每个正则对象的 `source` 属性。如果为 `'card'`，则为该 DOM 元素添加一个特定的 CSS class (如 `is-readonly`)，并禁用其编辑和删除按钮。对应的 CSS 样式应使其外观呈现为灰色或禁用状态。
