# World Info Optimizer (WIO) v3.0

基于设计文档 `docs/ui-activation-design.md` 的现代化重构版本。

## 项目结构

```
src/world_info_optimizer/
├── index.ts                    # 主入口文件，协调所有模块初始化
├── store.ts                    # 响应式状态管理（发布/订阅模式）
├── ui.ts                       # UI注入和渲染逻辑
├── events.ts                   # 事件处理器（控制器层）
├── core.ts                     # 业务逻辑层（CRUD操作）
├── api.ts                      # TavernHelper API封装
├── types.ts                    # TypeScript类型定义
├── tavern-helper-types.ts      # TavernHelper接口类型定义
├── constants.ts                # 常量定义
├── integration-test.ts         # 集成测试文件
└── ui/                         # UI子模块
    ├── views.ts                # 视图渲染函数
    ├── elements.ts             # UI元素创建函数
    ├── modals.ts               # 模态框管理
    └── helpers.ts              # UI辅助函数和响应式支持
```

## 核心特性

### 1. 模块化架构
- **关注点分离**: 每个模块负责特定功能
- **高内聚低耦合**: 模块间依赖清晰，易于维护
- **TypeScript支持**: 完整的类型安全

### 2. 响应式状态管理
- **发布/订阅模式**: 状态变化自动触发UI更新
- **单向数据流**: 可预测的状态变更
- **深度状态管理**: 正确处理Map和Set类型

### 3. 现代UI设计
- **响应式布局**: 支持移动端、平板和桌面
- **无障碍支持**: 完整的ARIA标签和键盘导航
- **高对比度模式**: 支持用户偏好设置
- **减少动画模式**: 尊重用户的动画偏好

### 4. 设备优化
- **触摸设备支持**: 自动检测并优化触摸交互
- **设备类型识别**: 针对不同设备类型的优化
- **屏幕尺寸适配**: 动态调整UI布局

## 主要改进

### 相比旧版本的优势

| 特性 | 旧版本 | 新版本 WIO v3.0 |
|------|--------|-----------------|
| **架构** | 单体文件 | 模块化设计 |
| **语言** | JavaScript | TypeScript |
| **状态管理** | 全局对象 | 响应式Store |
| **UI渲染** | 手动调用 | 状态驱动自动渲染 |
| **事件处理** | 混合在一起 | 独立事件模块 |
| **代码组织** | 单文件混杂 | 按功能分离 |

### 新增功能

1. **完整的类型安全**: 所有数据结构都有严格的TypeScript类型定义
2. **响应式设计**: 支持各种屏幕尺寸和设备类型
3. **无障碍支持**: 完整的键盘导航和屏幕阅读器支持
4. **设备优化**: 自动检测设备类型并应用相应优化
5. **高级状态管理**: 支持多选模式、搜索过滤器等高级功能

## 使用方法

### 初始化
```typescript
// 脚本会自动通过IIFE启动
// 等待SillyTavern环境就绪后自动初始化
```

### 状态管理
```typescript
import { getState, updateState, subscribe } from './store';

// 订阅状态变化
const unsubscribe = subscribe((newState) => {
    console.log('State updated:', newState);
});

// 更新状态
updateState(state => ({
    ...state,
    activeTab: 'global-lore'
}));
```

### 添加新功能
1. 在相应模块中添加功能函数
2. 在 `types.ts` 中定义相关类型
3. 在 `events.ts` 中添加事件处理
4. 在 `ui/views.ts` 中添加UI渲染逻辑

## 开发指南

### 代码规范
- 使用TypeScript严格模式
- 遵循函数式编程原则
- 保持模块间的清晰边界
- 添加完整的JSDoc注释

### 测试
运行集成测试：
```typescript
import { runIntegrationTest } from './integration-test';
runIntegrationTest();
```

### 调试
所有模块都包含详细的控制台日志，使用 `[WIO]` 前缀便于过滤。

## 兼容性

- **SillyTavern**: 兼容最新版本的TavernHelper API
- **浏览器**: 支持现代浏览器（ES2020+）
- **设备**: 支持桌面、平板和移动设备

## 更新日志

### v3.0 (当前版本)
- ✅ 完全重构为模块化架构
- ✅ 添加TypeScript类型安全
- ✅ 实现响应式状态管理
- ✅ 添加完整的响应式设计支持
- ✅ 实现无障碍功能
- ✅ 添加设备优化
- ✅ 创建完整的集成测试

## 贡献

欢迎提交Issue和Pull Request。请确保：
1. 遵循现有的代码风格
2. 添加适当的类型定义
3. 更新相关文档
4. 运行集成测试确保无错误
