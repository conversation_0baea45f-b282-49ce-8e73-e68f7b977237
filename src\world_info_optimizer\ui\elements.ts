// src/world_info_optimizer/ui/elements.ts

import { AppState, LorebookEntry, LorebookFile, TavernRegex } from '../types';
import { escapeHtml, highlightText } from './helpers';

export const createLorebookElement = (
  book: LorebookFile,
  state: AppState,
  searchTerm: string,
  isGlobal: boolean = true,
  entriesToShow?: LorebookEntry[],
): string => {
  const entries = entriesToShow !== undefined ? entriesToShow : state.lorebookEntries.get(book.name) || [];
  const totalEntries = state.lorebookEntries.get(book.name)?.length || 0;
  const usage = state.lorebookUsage.get(book.name) || [];
  const isCollapsed = state.collapsedBooks.has(book.name);

  const entryCountText =
    searchTerm && entries.length !== totalEntries ? `${entries.length} / ${totalEntries}` : `${totalEntries}`;

  // 折叠图标
  const collapseIcon = isCollapsed ? '▶' : '▼';

  return `
        <div class="wio-book-group" data-book-name="${escapeHtml(book.name)}">
            <div class="wio-book-header">
                <button class="wio-collapse-toggle" title="${isCollapsed ? '展开' : '折叠'}" aria-label="${isCollapsed ? '展开' : '折叠'} ${escapeHtml(book.name)}">${collapseIcon}</button>
                ${isGlobal ? `<input type="checkbox" ${book.enabled ? 'checked' : ''} class="wio-global-book-toggle" aria-label="启用 ${escapeHtml(book.name)}">` : ''}
                <h4>${highlightText(book.name, searchTerm)} <span class="wio-entry-count">(${entryCountText})</span></h4>
                ${usage.length > 0 ? `<span class="wio-usage-pill" title="被 ${usage.join(', ')} 使用" aria-label="被 ${usage.length} 个角色使用">${usage.length}</span>` : ''}
                <div class="wio-item-controls">
                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${escapeHtml(book.name)}"><i class="fa-solid fa-pencil"></i></button>
                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${escapeHtml(book.name)}"><i class="fa-solid fa-trash-can"></i></button>
                </div>
            </div>
            ${
              !isCollapsed
                ? `<div class="wio-entry-list">
                ${entries.map(entry => createEntryElement(entry, book.name, searchTerm, state)).join('')}
                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${escapeHtml(book.name)}" aria-label="在 ${escapeHtml(book.name)} 中新建条目">+ 新建条目</button></div>
            </div>`
                : ''
            }
        </div>
    `;
};

export const createEntryElement = (
  entry: LorebookEntry,
  bookName: string,
  searchTerm: string,
  state: AppState,
): string => {
  if (!entry) return ''; // 代码加固
  const displayName = entry.comment || '(未命名条目)';
  const itemId = `${bookName}/${entry.uid}`;
  const isSelected = state.selectedItems.has(itemId);

  return `
        <div class="wio-entry-item" data-uid="${entry.uid}" data-book-name="${escapeHtml(bookName)}" data-item-id="${escapeHtml(itemId)}">
            <div class="wio-entry-main">
                ${state.multiSelectMode ? `<input type="checkbox" ${isSelected ? 'checked' : ''} class="wio-multi-select-checkbox" aria-label="选择 ${escapeHtml(displayName)}">` : ''}
                <input type="checkbox" ${entry.enabled ? 'checked' : ''} class="wio-entry-toggle" aria-label="启用 ${escapeHtml(displayName)}">
                <span class="wio-entry-name">${highlightText(displayName, searchTerm)}</span>
                <span class="wio-entry-keys">${highlightText((entry.keys || []).join(', '), searchTerm)}</span>
            </div>
            <div class="wio-item-controls">
                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${escapeHtml(displayName)}"><i class="fa-solid fa-pencil"></i></button>
                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${escapeHtml(displayName)}"><i class="fa-solid fa-trash-can"></i></button>
            </div>
        </div>
    `;
};

export const createRegexItemElement = (regex: TavernRegex, searchTerm: string): string => {
  if (!regex) return ''; // 代码加固
  const displayName = regex.script_name || '(未命名正则)';
  const regexId =
    regex.id || `${regex.scope}-${btoa(unescape(encodeURIComponent(regex.script_name + regex.find_regex)))}`;

  return `
        <div class="wio-regex-item" data-id="${escapeHtml(regexId)}" data-scope="${regex.scope}">
            <div class="wio-regex-main">
                <input type="checkbox" ${regex.enabled ? 'checked' : ''} class="wio-regex-toggle" aria-label="启用 ${escapeHtml(displayName)}">
                <span class="wio-regex-name">${highlightText(displayName, searchTerm)} ${regex.source === 'card' ? '<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>' : ''}</span>
                <code class="wio-regex-find" title="查找: ${escapeHtml(regex.find_regex)}">${highlightText(regex.find_regex, searchTerm)}</code>
                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>
                <code class="wio-regex-replace" title="替换为: ${escapeHtml(regex.replace_string)}">${highlightText(regex.replace_string, searchTerm)}</code>
            </div>
            <div class="wio-item-controls">
                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${escapeHtml(displayName)}"><i class="fa-solid fa-pencil"></i></button>
                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${escapeHtml(displayName)}"><i class="fa-solid fa-trash-can"></i></button>
            </div>
        </div>
    `;
};
