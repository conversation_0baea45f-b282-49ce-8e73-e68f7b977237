### 4.1. 局部刷新角色数据 (Partial Refresh)

- **旧版实现**: 拥有独立的 `refreshCharacterData()` 函数。当用户切换角色或进行与角色相关的操作时，该函数只重新加载角色相关的正则、关联的世界书和聊天世界书，而不会重新加载所有全局世界书和设置。
- **新版现状**: 仅提供 `loadAllData()` 函数，该函数会重新加载所有数据。
- **功能差距**: 缺少针对角色切换场景的**性能优化**。在拥有大量全局世界书的情况下，每次切换角色都完全刷新可能会导致不必要的延迟。

### 4.2. 折叠当前角色的世界书 (Collapse Current)

- **旧版实现**: 定义了 `COLLAPSE_CURRENT_BTN_ID` 常量，并可能存在一个未被充分利用或在代码片段中实现的UI按钮，用于一键折叠当前角色正在使用的所有世界书。
- **新版现状**: 在 `constants.ts` 中同样定义了 `COLLAPSE_CURRENT_BTN_ID`，但该常量在整个代码库中**未被任何事件处理器或UI渲染逻辑所使用**。
- **功能差距**: 缺少一个便捷的UI功能，允许用户快速收起与当前角色相关的所有世界书，以专注于其他内容。虽然有“全部折叠”功能，但这不够精确。
