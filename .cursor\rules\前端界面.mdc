---
description: 当需要编写前端界面时, 你应该参考本文件
---
# 前端界面

如果 `src/xxx` 文件夹中既有 `index.ts` 文件也有 `index.html` 文件, 则它是一个前端界面项目.

前端界面以无沙盒 iframe 的形式在酒馆消息楼层中前台显示, 有一个自己的界面, 你可以在其中添加静态内容、样式、脚本等.

## index.html 中应该写什么

前端界面的 index.html 仅可填写静态 `<body>` 内容, 不得引用项目中其他文件, 所有非内嵌样式、代码、额外的外部依赖都应通过 Typescript 文件导入. 具体来说:

```html
<head>
  <!-- 保留一个什么都没有的 <head> 标签, webpack 打包时会在这里插入样式、脚本等 -->
</head>
<body>
  <!-- 这里写 <div>、<span> 等静态内容, 也可以只写 <div id="app"></div> 交给 vue 来渲染 -->
</body>
```

- 禁止在 `index.html` 中用 `<link rel="stylesheet" href="./index.css">` 导入样式, 而应该
  - (优先) 设计 vue 组件, 在 vue 组件中用 `<style lang="scss">` 书写.
  - 或在 Typescript 文件中用 `import './index.css'` 导入, 这样导入的样式将会经过打包最小化后插入到 `<head>` 部分;
- 禁止在 `index.html` 中用 `<script src="./index.ts">` 来引用 `index.ts` 或其他本地脚本. `index.ts` 及它导入的文件会由 webpack 直接加入到最终打包好的 `dist/**/index.html` 中.
- 在 `index.html` 中填入 `<img>` 标签时, 禁止使用 `src=""` 占位. 要么引用实际的图片, 要么不要有这个属性, 否则会导致 webpack 打包错误.

## 样式设计

### 简单内嵌样式

对于简单的样式, 你可以在 `index.html` 中直接使用 tailwindcss 书写.

### 复杂外链样式

如果样式复杂到需要使用 `<style>` 标签, 则你不应该直接在 html 里书写 `<style>`, 而应该

- (优先) 设计 vue 组件, 并在 Typescript 文件中 `import Component from './文件.vue'` 再将其 `mount` 到界面上.
- 或新建 scss 文件并在 Typescript 文件中以 `import './index.scss'` 的形式应用到界面上.

### iframe 适配要求

- 当对前端界面高度进行调整时, 禁止使用 `vh` 单位等会受宿主高度影响的单位, 而是使用 `width` 和 `aspect-ratio` 来让高度根据宽度动态调整.
- 避免使用会强制撑高父容器的元素 (如 `min-height`、`overflow: auto`)
- 页面整体应适配容器宽度，不产生横向滚动条
- 如果样式更适合卡片形状，则不要有背景颜色，除非用户有明确要求
