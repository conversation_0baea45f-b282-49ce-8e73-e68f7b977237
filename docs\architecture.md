# Architecture Document for World Info Optimizer

This document outlines the software architecture for the World Info Optimizer project.

## 1. Overview

The project is structured to separate core logic from UI and to provide clear examples for developers. The main logic is contained within the `src/world_info_optimizer` directory, while `src/脚本示例` and `src/界面示例` provide usage examples.

## 2. Project Structure

The `src` directory is organized as follows:

- **`src/`**
  - **`脚本示例/`**: Contains example scripts demonstrating how to integrate with the Tavern scripting system.
    - `调整消息楼层.ts`
    - `加载和卸载时执行函数.ts`
    - `监听消息修改.ts`
    - `添加按钮和注册按钮事件.ts`
    - `Example_OldVersion.js`
    - `index.ts`
  - **`界面示例/`**: Contains example UI components and integration logic.
    - `加载和卸载时执行函数.ts`
    - `日记.vue`
    - `选择框.vue`
    - `渲染vue界面.ts`
    - `app.vue`
    - `index.html`
    - `index.ts`
  - **`world_info_optimizer/`**: The core logic of the plugin.
    - `api.ts`: Exposes the plugin's API.
    - `constants.ts`: Defines constants used throughout the project.
    - `core.ts`: Contains the core business logic. This includes fetching and processing all data (both global and character-specific), managing state transitions via `store.ts`, and implementing all user-facing features like search, replace, and bulk operations. It is designed to be robust, handling loading states and errors gracefully. Asynchronous operations, especially bulk actions, are optimized to run in parallel for better performance.
    - `events.ts`: Handles event listening and processing.
    - `index.ts`: The main entry point of the plugin.
    - `store.ts`: Manages the plugin's state.
    - `types.ts`: Defines TypeScript types.
    - `ui.ts`: Manages user interface elements and interactions.
    - `ui/notifications.ts`: Manages non-blocking user action feedback notifications (Toasts).
